# -*- coding: utf-8 -*-
"""
AI Learning & Optimization Module for AIFXHunter Pro v2.2
Analyzes signal performance and optimizes strategy parameters
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import logging
from dataclasses import dataclass
from collections import defaultdict
import sqlite3

logger = logging.getLogger(__name__)

@dataclass
class LearningInsight:
    """Data class for AI learning insights"""
    insight_type: str
    description: str
    confidence: float
    impact_score: float
    recommendation: str
    data_points: int
    timestamp: datetime

class AILearningOptimizer:
    """
    AI Learning and Optimization system that analyzes signal performance
    and provides recommendations for strategy improvement
    """
    
    def __init__(self, performance_database, performance_evaluator):
        self.performance_database = performance_database
        self.performance_evaluator = performance_evaluator
        
        # Learning configuration
        self.min_samples_for_learning = 10
        self.confidence_threshold = 0.7
        self.learning_window_days = 30
        
        # Learning data storage
        self.learning_insights = []
        self.pattern_cache = {}
        self.optimization_history = []
        
        logger.info("🧠 AI Learning Optimizer initialized")
    
    def analyze_signal_patterns(self, days: int = 7) -> Dict[str, Any]:
        """Analyze patterns in winning vs losing signals"""
        try:
            # Get recent signal results
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with sqlite3.connect(self.performance_database.db_path) as conn:
                query = """
                SELECT s.*, sr.result, sr.exit_price
                FROM signals s
                JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.timestamp >= ?
                ORDER BY s.timestamp DESC
                """
                
                df = pd.read_sql_query(query, conn, params=(cutoff_date,))

                # Fix timestamp parsing issues
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'], format='mixed', errors='coerce')
            
            if len(df) < self.min_samples_for_learning:
                return {'status': 'insufficient_data', 'message': f'Need at least {self.min_samples_for_learning} signals for analysis'}
            
            # Analyze patterns
            patterns = {
                'time_patterns': self._analyze_time_patterns(df),
                'confidence_patterns': self._analyze_confidence_patterns(df),
                'asset_patterns': self._analyze_asset_patterns(df),
                'strategy_patterns': self._analyze_strategy_patterns(df),
                'market_condition_patterns': self._analyze_market_condition_patterns(df),
                'expiry_time_patterns': self._analyze_expiry_patterns(df)
            }
            
            # Generate insights
            insights = self._generate_insights_from_patterns(patterns)
            
            # Store insights
            self.learning_insights.extend(insights)
            
            return {
                'status': 'success',
                'analysis_period': f'{days} days',
                'total_signals': len(df),
                'patterns': patterns,
                'insights': [self._insight_to_dict(insight) for insight in insights],
                'recommendations': self._generate_recommendations(insights)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing signal patterns: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _analyze_time_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance patterns by time of day/week"""
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['win'] = (df['result'] == 'WIN').astype(int)
        
        # Hourly performance
        hourly_performance = df.groupby('hour').agg({
            'win': ['count', 'sum', 'mean']
        }).round(3)
        
        # Daily performance
        daily_performance = df.groupby('day_of_week').agg({
            'win': ['count', 'sum', 'mean']
        }).round(3)
        
        # Find best and worst times
        hourly_win_rates = hourly_performance['win']['mean']
        best_hours = hourly_win_rates.nlargest(3).index.tolist()
        worst_hours = hourly_win_rates.nsmallest(3).index.tolist()
        
        daily_win_rates = daily_performance['win']['mean']
        best_days = daily_win_rates.nlargest(2).index.tolist()
        worst_days = daily_win_rates.nsmallest(2).index.tolist()
        
        return {
            'hourly_performance': hourly_performance.to_dict(),
            'daily_performance': daily_performance.to_dict(),
            'best_hours': best_hours,
            'worst_hours': worst_hours,
            'best_days': best_days,
            'worst_days': worst_days,
            'peak_performance_hour': int(hourly_win_rates.idxmax()),
            'lowest_performance_hour': int(hourly_win_rates.idxmin())
        }
    
    def _analyze_confidence_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance patterns by confidence levels"""
        df['confidence_range'] = pd.cut(df['confidence'], 
                                       bins=[0, 60, 70, 80, 90, 100], 
                                       labels=['<60%', '60-70%', '70-80%', '80-90%', '90%+'])
        
        confidence_performance = df.groupby('confidence_range').agg({
            'result': lambda x: (x == 'WIN').mean(),
            'confidence': ['count', 'mean']
        }).round(3)
        
        # Analyze confidence vs actual performance
        confidence_accuracy = df.groupby(pd.cut(df['confidence'], bins=10)).agg({
            'result': lambda x: (x == 'WIN').mean(),
            'confidence': 'mean'
        }).dropna()
        
        # Calculate confidence calibration
        calibration_error = abs(confidence_accuracy['confidence']/100 - confidence_accuracy['result']).mean()
        
        return {
            'confidence_performance': confidence_performance.to_dict(),
            'confidence_accuracy': confidence_accuracy.to_dict(),
            'calibration_error': calibration_error,
            'overconfident': calibration_error > 0.1,
            'best_confidence_range': confidence_performance['result'].idxmax(),
            'worst_confidence_range': confidence_performance['result'].idxmin()
        }
    
    def _analyze_asset_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance patterns by asset"""
        asset_performance = df.groupby('asset').agg({
            'result': lambda x: (x == 'WIN').mean(),
            'confidence': ['count', 'mean']
        }).round(3)
        
        # Sort by win rate
        asset_performance = asset_performance.sort_values(('result', '<lambda>'), ascending=False)
        
        best_assets = asset_performance.head(3).index.tolist()
        worst_assets = asset_performance.tail(3).index.tolist()
        
        return {
            'asset_performance': asset_performance.to_dict(),
            'best_assets': best_assets,
            'worst_assets': worst_assets,
            'asset_count': len(asset_performance)
        }
    
    def _analyze_strategy_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance patterns by strategy"""
        strategy_performance = df.groupby('strategy_name').agg({
            'result': lambda x: (x == 'WIN').mean(),
            'confidence': ['count', 'mean']
        }).round(3)
        
        strategy_performance = strategy_performance.sort_values(('result', '<lambda>'), ascending=False)
        
        best_strategies = strategy_performance.head(3).index.tolist()
        worst_strategies = strategy_performance.tail(3).index.tolist()
        
        return {
            'strategy_performance': strategy_performance.to_dict(),
            'best_strategies': best_strategies,
            'worst_strategies': worst_strategies,
            'strategy_count': len(strategy_performance)
        }
    
    def _analyze_market_condition_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance patterns by market conditions"""
        # Parse market conditions JSON
        market_conditions = []
        for idx, row in df.iterrows():
            try:
                conditions = json.loads(row['market_conditions']) if row['market_conditions'] else {}
                conditions['result'] = row['result']
                market_conditions.append(conditions)
            except:
                continue
        
        if not market_conditions:
            return {'status': 'no_market_data'}
        
        mc_df = pd.DataFrame(market_conditions)
        
        patterns = {}
        for column in ['trend', 'volatility', 'momentum']:
            if column in mc_df.columns:
                pattern = mc_df.groupby(column).agg({
                    'result': lambda x: (x == 'WIN').mean()
                }).round(3)
                patterns[f'{column}_performance'] = pattern.to_dict()
        
        return patterns
    
    def _analyze_expiry_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance patterns by expiry time"""
        expiry_performance = df.groupby('expiry_minutes').agg({
            'result': lambda x: (x == 'WIN').mean(),
            'confidence': ['count', 'mean']
        }).round(3)
        
        expiry_performance = expiry_performance.sort_values(('result', '<lambda>'), ascending=False)
        
        best_expiry = expiry_performance.head(1).index.tolist()
        worst_expiry = expiry_performance.tail(1).index.tolist()
        
        return {
            'expiry_performance': expiry_performance.to_dict(),
            'best_expiry_time': best_expiry[0] if best_expiry else None,
            'worst_expiry_time': worst_expiry[0] if worst_expiry else None
        }
    
    def _generate_insights_from_patterns(self, patterns: Dict[str, Any]) -> List[LearningInsight]:
        """Generate actionable insights from pattern analysis"""
        insights = []
        
        # Time-based insights
        if 'time_patterns' in patterns:
            time_data = patterns['time_patterns']
            if 'best_hours' in time_data and time_data['best_hours']:
                insights.append(LearningInsight(
                    insight_type='time_optimization',
                    description=f"Best performance hours: {time_data['best_hours']}",
                    confidence=0.8,
                    impact_score=0.7,
                    recommendation=f"Focus signal generation during hours {time_data['best_hours']}",
                    data_points=len(time_data.get('hourly_performance', {})),
                    timestamp=datetime.now()
                ))
        
        # Confidence calibration insights
        if 'confidence_patterns' in patterns:
            conf_data = patterns['confidence_patterns']
            if conf_data.get('overconfident', False):
                insights.append(LearningInsight(
                    insight_type='confidence_calibration',
                    description="Model appears overconfident in predictions",
                    confidence=0.9,
                    impact_score=0.8,
                    recommendation="Reduce confidence scores by 10-15% for better calibration",
                    data_points=100,
                    timestamp=datetime.now()
                ))
        
        # Asset performance insights
        if 'asset_patterns' in patterns:
            asset_data = patterns['asset_patterns']
            if 'best_assets' in asset_data and asset_data['best_assets']:
                insights.append(LearningInsight(
                    insight_type='asset_selection',
                    description=f"Top performing assets: {asset_data['best_assets'][:2]}",
                    confidence=0.85,
                    impact_score=0.6,
                    recommendation=f"Prioritize signals for {', '.join(asset_data['best_assets'][:2])}",
                    data_points=asset_data.get('asset_count', 0),
                    timestamp=datetime.now()
                ))
        
        return insights
    
    def _generate_recommendations(self, insights: List[LearningInsight]) -> List[str]:
        """Generate actionable recommendations from insights"""
        recommendations = []
        
        for insight in insights:
            if insight.confidence > self.confidence_threshold and insight.impact_score > 0.5:
                recommendations.append(insight.recommendation)
        
        return recommendations
    
    def _insight_to_dict(self, insight: LearningInsight) -> Dict[str, Any]:
        """Convert LearningInsight to dictionary"""
        return {
            'type': insight.insight_type,
            'description': insight.description,
            'confidence': insight.confidence,
            'impact_score': insight.impact_score,
            'recommendation': insight.recommendation,
            'data_points': insight.data_points,
            'timestamp': insight.timestamp.isoformat()
        }
    
    def optimize_strategy_parameters(self, strategy_name: str) -> Dict[str, Any]:
        """Optimize parameters for a specific strategy based on historical performance"""
        try:
            # Get strategy performance data
            strategy_stats = self.performance_evaluator.evaluate_strategy_performance(strategy_name)
            
            if not strategy_stats or strategy_stats.get('total_signals', 0) < self.min_samples_for_learning:
                return {'status': 'insufficient_data'}
            
            # Analyze parameter effectiveness
            optimization_results = {
                'current_performance': strategy_stats,
                'recommended_changes': [],
                'expected_improvement': 0.0
            }
            
            # Confidence threshold optimization
            if strategy_stats.get('win_rate', 0) < 60:
                optimization_results['recommended_changes'].append({
                    'parameter': 'confidence_threshold',
                    'current': 'auto',
                    'recommended': 'increase by 10%',
                    'reason': 'Low win rate suggests need for higher confidence threshold'
                })
                optimization_results['expected_improvement'] += 5.0
            
            # Expiry time optimization
            if 'expiry_patterns' in self.pattern_cache:
                best_expiry = self.pattern_cache['expiry_patterns'].get('best_expiry_time')
                if best_expiry:
                    optimization_results['recommended_changes'].append({
                        'parameter': 'default_expiry',
                        'current': '5m',
                        'recommended': f'{best_expiry}m',
                        'reason': f'{best_expiry}m expiry shows best historical performance'
                    })
                    optimization_results['expected_improvement'] += 3.0
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error optimizing strategy parameters: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def get_learning_summary(self) -> Dict[str, Any]:
        """Get summary of AI learning progress and insights"""
        return {
            'total_insights': len(self.learning_insights),
            'recent_insights': [self._insight_to_dict(insight) for insight in self.learning_insights[-5:]],
            'learning_status': 'active' if len(self.learning_insights) > 0 else 'initializing',
            'optimization_count': len(self.optimization_history),
            'last_analysis': max([insight.timestamp for insight in self.learning_insights]).isoformat() if self.learning_insights else None
        }
