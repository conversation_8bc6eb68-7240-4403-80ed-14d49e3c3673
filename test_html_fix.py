#!/usr/bin/env python3
"""
Test script to verify the HTML comment fix for live signals display
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from live_signal_manager import LiveSignalManager
from automated_trading_bot import TradingSignal
from datetime import datetime, timedelta

def test_html_generation():
    """Test HTML generation without comments"""
    print("🔍 Testing HTML Generation Fix...")
    
    # Create test signal
    signal = TradingSignal(
        signal_id='TEST_001',
        asset='EURUSD',
        action='CALL',
        entry_price=1.0850,
        expiry_time=datetime.now() + timedelta(minutes=5),
        expiry_minutes=5,
        confidence=85.0,
        strategy_name='AI_Test_Strategy',
        reasoning=['Test signal'],
        timestamp=datetime.now(),
        risk_level='Medium',
        market_conditions={}
    )
    
    # Test HTML generation
    lsm = LiveSignalManager()
    lsm.add_signal(signal)
    
    html = lsm.generate_live_signals_html('all')
    
    print(f"✅ HTML Generation Results:")
    print(f"📊 HTML Length: {len(html)} characters")
    print(f"🔍 Contains HTML comments: {'<!--' in html}")
    print(f"🎯 Contains signal data: {'EURUSD' in html}")
    print(f"⏰ Contains timer elements: {'timer-' in html}")
    print(f"🎨 Contains styling: {'background:' in html}")
    
    # Check for issues
    issues = []
    if '<!--' in html:
        issues.append("❌ HTML still contains comments")
    if 'EURUSD' not in html:
        issues.append("❌ Signal data missing")
    if 'timer-' not in html:
        issues.append("❌ Timer elements missing")
    if 'background:' not in html:
        issues.append("❌ Styling missing")
    
    if issues:
        print("\n🚨 ISSUES FOUND:")
        for issue in issues:
            print(f"  {issue}")
        return False
    else:
        print("\n✅ ALL TESTS PASSED - HTML generation working correctly!")
        return True

def test_empty_signals():
    """Test HTML generation with no signals"""
    print("\n🔍 Testing Empty Signals Display...")
    
    lsm = LiveSignalManager()
    html = lsm.generate_live_signals_html('all')
    
    print(f"📊 Empty HTML Length: {len(html)} characters")
    print(f"🤖 Contains bot message: {'AI Bot is Analyzing' in html}")
    
    if 'AI Bot is Analyzing' in html and '<!--' not in html:
        print("✅ Empty signals display working correctly!")
        return True
    else:
        print("❌ Empty signals display has issues")
        return False

if __name__ == "__main__":
    print("🧪 HTML Fix Verification Test")
    print("=" * 50)
    
    test1_passed = test_html_generation()
    test2_passed = test_empty_signals()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED - Fix is working correctly!")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED - Fix needs more work")
        sys.exit(1)
