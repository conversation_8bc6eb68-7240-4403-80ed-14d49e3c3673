# -*- coding: utf-8 -*-
"""
Live Signal Manager for AIFXHunter Pro v2.2
Manages real-time signal display, updates, and countdown timers
"""

import streamlit as st
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading
import queue
from dataclasses import asdict

class LiveSignalManager:
    """
    Manages live signal display and real-time updates for the trading bot
    """
    
    def __init__(self):
        self.live_signals = []
        self.signal_updates_queue = queue.Queue()
        self.update_thread = None
        self.is_updating = False
        
        # Display configuration
        self.max_displayed_signals = 10
        self.update_interval = 1.0  # Update every second
        
        # Signal categories for filtering
        self.signal_categories = {
            'all': 'All Signals',
            'high_confidence': 'High Confidence (≥80%)',
            'medium_confidence': 'Medium Confidence (60-79%)',
            'call_signals': 'CALL Signals Only',
            'put_signals': 'PUT Signals Only'
        }
    
    def add_signal(self, signal):
        """Add a new signal to the live display"""
        self.live_signals.append(signal)
        self.signal_updates_queue.put(('add', signal))
        
        # Keep only the most recent signals
        if len(self.live_signals) > self.max_displayed_signals:
            removed_signal = self.live_signals.pop(0)
            self.signal_updates_queue.put(('remove', removed_signal))
    
    def update_signal(self, signal_id: str, updates: Dict[str, Any]):
        """Update an existing signal"""
        for i, signal in enumerate(self.live_signals):
            if signal.signal_id == signal_id:
                # Update signal attributes
                for key, value in updates.items():
                    if hasattr(signal, key):
                        setattr(signal, key, value)
                
                self.signal_updates_queue.put(('update', signal))
                break
    
    def remove_signal(self, signal_id: str):
        """Remove a signal from live display"""
        for i, signal in enumerate(self.live_signals):
            if signal.signal_id == signal_id:
                removed_signal = self.live_signals.pop(i)
                self.signal_updates_queue.put(('remove', removed_signal))
                break
    
    def get_filtered_signals(self, filter_type: str = 'all') -> List:
        """Get signals filtered by type"""
        if filter_type == 'all':
            return self.live_signals
        elif filter_type == 'high_confidence':
            return [s for s in self.live_signals if s.confidence >= 80]
        elif filter_type == 'medium_confidence':
            return [s for s in self.live_signals if 60 <= s.confidence < 80]
        elif filter_type == 'call_signals':
            return [s for s in self.live_signals if s.action == 'CALL']
        elif filter_type == 'put_signals':
            return [s for s in self.live_signals if s.action == 'PUT']
        else:
            return self.live_signals
    
    def generate_live_signals_html(self, filter_type: str = 'all') -> str:
        """Generate HTML for live signals display with countdown timers"""
        signals = self.get_filtered_signals(filter_type)
        
        if not signals:
            return """
            <div style="text-align: center; padding: 2rem; color: rgba(255,255,255,0.6);">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🤖</div>
                <div style="font-size: 1.2rem; font-weight: 600;">AI Bot is Analyzing Markets...</div>
                <div style="font-size: 0.9rem; margin-top: 0.5rem;">Waiting for trading opportunities</div>
            </div>
            """
        
        html_content = ""
        timer_data = []
        
        for signal in signals:
            # Calculate time remaining
            now = datetime.now()
            time_remaining = signal.expiry_time - now
            
            if time_remaining.total_seconds() > 0:
                minutes_left = int(time_remaining.total_seconds() / 60)
                seconds_left = int(time_remaining.total_seconds() % 60)
                
                # Determine color and urgency based on time remaining
                if time_remaining.total_seconds() < 30:
                    color = "#ef4444"  # Red - urgent
                    icon = "🚨"
                    urgency_class = "urgent-signal"
                elif time_remaining.total_seconds() < 120:
                    color = "#f59e0b"  # Orange - warning
                    icon = "⚠️"
                    urgency_class = "warning-signal"
                elif time_remaining.total_seconds() < 300:
                    color = "#eab308"  # Yellow - caution
                    icon = "⏰"
                    urgency_class = "caution-signal"
                else:
                    color = "#10b981"  # Green - normal
                    icon = "⏳"
                    urgency_class = "normal-signal"
                
                # Calculate progress percentage
                total_duration = (signal.expiry_time - signal.timestamp).total_seconds()
                elapsed_time = (now - signal.timestamp).total_seconds()
                progress_percent = (elapsed_time / total_duration * 100) if total_duration > 0 else 0
                
                # Generate unique IDs for real-time updates
                card_id = f"signal-card-{signal.signal_id}"
                timer_id = f"timer-{signal.signal_id}"
                progress_id = f"progress-{signal.signal_id}"
                icon_id = f"icon-{signal.signal_id}"
                
                # Store timer data for JavaScript
                timer_data.append({
                    'signal_id': signal.signal_id,
                    'expiry_time': signal.expiry_time.isoformat(),
                    'card_id': card_id,
                    'timer_id': timer_id,
                    'progress_id': progress_id,
                    'icon_id': icon_id,
                    'total_duration': total_duration * 1000  # Convert to milliseconds
                })
                
                # Action-specific styling
                action_color = "#10b981" if signal.action == "CALL" else "#ef4444"
                action_icon = "📞" if signal.action == "CALL" else "📉"
                
                html_content += f"""
                <div id="{card_id}" class="live-signal-card {urgency_class}" 
                     style="background: linear-gradient(135deg, {color}15, {color}08); 
                            padding: 1.2rem; border-radius: 12px; margin: 0.8rem 0; 
                            border: 2px solid {color}40; transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                    
                    <!-- Signal Header -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span id="{icon_id}" style="font-size: 1.2rem;">{icon}</span>
                            <div>
                                <div style="color: {action_color}; font-weight: 700; font-size: 1.1rem;">
                                    {action_icon} {signal.asset} • {signal.action}
                                </div>
                                <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">
                                    {signal.strategy_name[:25]}{'...' if len(signal.strategy_name) > 25 else ''}
                                </div>
                            </div>
                        </div>
                        <div id="{timer_id}" style="color: {color}; font-size: 1rem; font-weight: 700; text-align: right;">
                            {minutes_left}m {seconds_left}s
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 10px; height: 8px; overflow: hidden; margin: 0.8rem 0;">
                        <div id="{progress_id}" style="height: 100%; width: {progress_percent}%; 
                             background: linear-gradient(90deg, {color}, {color}80); 
                             border-radius: 10px; transition: width 0.3s ease;"></div>
                    </div>
                    
                    <!-- Signal Details -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.8rem; margin: 0.8rem 0;">
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Entry Price</div>
                            <div style="color: #3b82f6; font-size: 0.85rem; font-weight: 600;">{signal.entry_price:.5f}</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Confidence</div>
                            <div style="color: #8b5cf6; font-size: 0.85rem; font-weight: 600;">{signal.confidence:.0f}%</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Expiry</div>
                            <div style="color: #f59e0b; font-size: 0.85rem; font-weight: 600;">{signal.expiry_minutes}m</div>
                        </div>
                    </div>
                    
                    <!-- Risk Level Indicator -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.8rem;">
                        <div style="display: flex; align-items: center; gap: 0.3rem;">
                            <div style="color: rgba(255,255,255,0.7); font-size: 0.75rem;">Risk:</div>
                            <div style="color: {'#10b981' if signal.risk_level == 'Low' else '#f59e0b' if signal.risk_level == 'Medium' else '#ef4444'}; 
                                        font-size: 0.75rem; font-weight: 600;">{signal.risk_level}</div>
                        </div>
                        <div style="color: rgba(255,255,255,0.6); font-size: 0.7rem;">
                            {signal.timestamp.strftime('%H:%M:%S')}
                        </div>
                    </div>
                </div>
                """
            
            else:
                # Expired signal
                html_content += f"""
                <div class="expired-signal-card" 
                     style="background: rgba(107, 114, 128, 0.2); padding: 1rem; border-radius: 8px; 
                            margin: 0.5rem 0; border: 1px solid #6b728040; opacity: 0.7;">
                    <div style="color: #6b7280; font-weight: 600;">
                        ⏰ {signal.asset} • {signal.action} • EXPIRED
                    </div>
                </div>
                """
        
        # Add JavaScript for real-time countdown updates
        if timer_data:
            timer_data_json = json.dumps(timer_data)
            countdown_js = f"""
            <script>
            // Real-time countdown system for live signals
            const liveTimerData = {timer_data_json};
            let liveCountdownIntervals = [];
            
            // Clear existing intervals
            liveCountdownIntervals.forEach(interval => clearInterval(interval));
            liveCountdownIntervals = [];
            
            function updateLiveCountdown(data) {{
                const now = new Date().getTime();
                const expiryTime = new Date(data.expiry_time).getTime();
                const timeRemaining = expiryTime - now;
                
                if (timeRemaining > 0) {{
                    const minutes = Math.floor(timeRemaining / (1000 * 60));
                    const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);
                    
                    // Update timer display
                    const timerElement = document.getElementById(data.timer_id);
                    if (timerElement) {{
                        timerElement.textContent = minutes + 'm ' + seconds + 's';
                    }}
                    
                    // Update progress bar
                    const progressElement = document.getElementById(data.progress_id);
                    if (progressElement) {{
                        const elapsed = data.total_duration - timeRemaining;
                        const progressPercent = (elapsed / data.total_duration) * 100;
                        progressElement.style.width = progressPercent + '%';
                    }}
                    
                    // Update urgency styling
                    const cardElement = document.getElementById(data.card_id);
                    const iconElement = document.getElementById(data.icon_id);
                    
                    if (cardElement && iconElement) {{
                        if (timeRemaining < 30000) {{ // < 30 seconds
                            cardElement.style.borderColor = '#ef444460';
                            cardElement.style.background = 'linear-gradient(135deg, #ef444420, #ef444410)';
                            iconElement.textContent = '🚨';
                            cardElement.style.animation = 'pulse 1s infinite';
                        }} else if (timeRemaining < 120000) {{ // < 2 minutes
                            cardElement.style.borderColor = '#f59e0b60';
                            cardElement.style.background = 'linear-gradient(135deg, #f59e0b20, #f59e0b10)';
                            iconElement.textContent = '⚠️';
                        }}
                    }}
                }} else {{
                    // Signal expired
                    const cardElement = document.getElementById(data.card_id);
                    if (cardElement) {{
                        cardElement.style.opacity = '0.5';
                        cardElement.innerHTML = '<div style="color: #6b7280; text-align: center; padding: 1rem;">⏰ Signal Expired</div>';
                    }}
                }}
            }}
            
            // Start countdown timers
            liveTimerData.forEach(data => {{
                updateLiveCountdown(data);
                const interval = setInterval(() => {{
                    updateLiveCountdown(data);
                }}, 1000);
                liveCountdownIntervals.push(interval);
            }});
            
            // Add pulse animation for urgent signals
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {{
                    0% {{ transform: scale(1); }}
                    50% {{ transform: scale(1.02); }}
                    100% {{ transform: scale(1); }}
                }}
            `;
            document.head.appendChild(style);
            </script>
            """
            html_content += countdown_js
        
        return html_content
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """Get statistics about current live signals"""
        if not self.live_signals:
            return {}
        
        total_signals = len(self.live_signals)
        call_signals = len([s for s in self.live_signals if s.action == 'CALL'])
        put_signals = len([s for s in self.live_signals if s.action == 'PUT'])
        
        avg_confidence = sum(s.confidence for s in self.live_signals) / total_signals
        
        # Time to expiry statistics
        now = datetime.now()
        expiry_times = [(s.expiry_time - now).total_seconds() for s in self.live_signals]
        expiry_times = [t for t in expiry_times if t > 0]  # Only active signals
        
        return {
            'total_signals': total_signals,
            'call_signals': call_signals,
            'put_signals': put_signals,
            'avg_confidence': avg_confidence,
            'active_signals': len(expiry_times),
            'avg_time_to_expiry': sum(expiry_times) / len(expiry_times) if expiry_times else 0,
            'urgent_signals': len([t for t in expiry_times if t < 30]),  # < 30 seconds
            'warning_signals': len([t for t in expiry_times if 30 <= t < 120])  # 30s - 2min
        }
