# -*- coding: utf-8 -*-
"""
Live Signal Manager for AIFXHunter Pro v2.2
Manages real-time signal display, updates, and countdown timers
"""

import streamlit as st
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading
import queue
from dataclasses import asdict

class LiveSignalManager:
    """
    Manages live signal display and real-time updates for the trading bot
    """
    
    def __init__(self):
        self.live_signals = []
        self.expired_signals = []  # Store expired signals with results
        self.signal_updates_queue = queue.Queue()
        self.update_thread = None
        self.is_updating = False

        # Display configuration
        self.max_displayed_signals = 10
        self.max_expired_signals = 50  # Keep last 50 expired signals
        self.update_interval = 1.0  # Update every second

        # Signal categories for filtering
        self.signal_categories = {
            'all': 'All Signals',
            'high_confidence': 'High Confidence (≥80%)',
            'medium_confidence': 'Medium Confidence (60-79%)',
            'call_signals': 'CALL Signals Only',
            'put_signals': 'PUT Signals Only'
        }

        # Expired signal categories
        self.expired_categories = {
            'all': 'All Expired',
            'win': 'WIN Signals',
            'loss': 'LOSS Signals',
            'pending': 'Pending Results',
            'today': 'Today Only',
            'yesterday': 'Yesterday Only',
            'this_week': 'This Week'
        }
    
    def add_signal(self, signal):
        """Add a new signal to the live display"""
        self.live_signals.append(signal)
        self.signal_updates_queue.put(('add', signal))
        
        # Keep only the most recent signals
        if len(self.live_signals) > self.max_displayed_signals:
            removed_signal = self.live_signals.pop(0)
            self.signal_updates_queue.put(('remove', removed_signal))
    
    def update_signal(self, signal_id: str, updates: Dict[str, Any]):
        """Update an existing signal"""
        for i, signal in enumerate(self.live_signals):
            if signal.signal_id == signal_id:
                # Update signal attributes
                for key, value in updates.items():
                    if hasattr(signal, key):
                        setattr(signal, key, value)
                
                self.signal_updates_queue.put(('update', signal))
                break
    
    def remove_signal(self, signal_id: str):
        """Remove a signal from live display"""
        for i, signal in enumerate(self.live_signals):
            if signal.signal_id == signal_id:
                removed_signal = self.live_signals.pop(i)
                self.signal_updates_queue.put(('remove', removed_signal))
                break

    def expire_signal(self, signal_id: str, result: str = 'PENDING', entry_price: float = None, exit_price: float = None):
        """Move signal to expired list with result"""
        for i, signal in enumerate(self.live_signals):
            if signal.signal_id == signal_id:
                expired_signal = self.live_signals.pop(i)

                # Add result information
                expired_signal.result = result
                expired_signal.entry_price_actual = entry_price or expired_signal.entry_price
                expired_signal.exit_price = exit_price
                expired_signal.expired_time = datetime.now()
                expired_signal.profit_loss = self._calculate_profit_loss(expired_signal, exit_price) if exit_price else 0.0

                # Add to expired signals
                self.expired_signals.append(expired_signal)

                # Keep only recent expired signals
                if len(self.expired_signals) > self.max_expired_signals:
                    self.expired_signals = self.expired_signals[-self.max_expired_signals:]

                break

    def update_signal_result(self, signal_id: str, result: str, exit_price: float = None):
        """Update result for an expired signal"""
        for signal in self.expired_signals:
            if signal.signal_id == signal_id:
                signal.result = result
                signal.exit_price = exit_price
                signal.profit_loss = self._calculate_profit_loss(signal, exit_price) if exit_price else 0.0
                break

    def _calculate_profit_loss(self, signal, exit_price: float) -> float:
        """Calculate profit/loss for binary options"""
        if not exit_price or not hasattr(signal, 'entry_price_actual'):
            return 0.0

        entry_price = signal.entry_price_actual

        if signal.action == 'CALL':
            # CALL wins if exit price > entry price
            return 1.0 if exit_price > entry_price else -1.0
        else:  # PUT
            # PUT wins if exit price < entry price
            return 1.0 if exit_price < entry_price else -1.0
    
    def get_filtered_signals(self, filter_type: str = 'all') -> List:
        """Get signals filtered by type"""
        if filter_type == 'all':
            return self.live_signals
        elif filter_type == 'high_confidence':
            return [s for s in self.live_signals if s.confidence >= 80]
        elif filter_type == 'medium_confidence':
            return [s for s in self.live_signals if 60 <= s.confidence < 80]
        elif filter_type == 'call_signals':
            return [s for s in self.live_signals if s.action == 'CALL']
        elif filter_type == 'put_signals':
            return [s for s in self.live_signals if s.action == 'PUT']
        else:
            return self.live_signals

    def get_filtered_expired_signals(self, filter_type: str = 'all', date_filter: str = 'all') -> List:
        """Get expired signals filtered by result and date"""
        from datetime import timedelta

        signals = self.expired_signals.copy()

        # Filter by result type
        if filter_type == 'win':
            signals = [s for s in signals if getattr(s, 'result', 'PENDING') == 'WIN']
        elif filter_type == 'loss':
            signals = [s for s in signals if getattr(s, 'result', 'PENDING') == 'LOSS']
        elif filter_type == 'pending':
            signals = [s for s in signals if getattr(s, 'result', 'PENDING') == 'PENDING']

        # Filter by date
        now = datetime.now()
        if date_filter == 'today':
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            signals = [s for s in signals if getattr(s, 'expired_time', s.timestamp) >= today_start]
        elif date_filter == 'yesterday':
            yesterday_start = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            yesterday_end = now.replace(hour=0, minute=0, second=0, microsecond=0)
            signals = [s for s in signals if yesterday_start <= getattr(s, 'expired_time', s.timestamp) < yesterday_end]
        elif date_filter == 'this_week':
            week_start = now - timedelta(days=now.weekday())
            week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            signals = [s for s in signals if getattr(s, 'expired_time', s.timestamp) >= week_start]

        # Sort by expiry time (newest first)
        signals.sort(key=lambda x: getattr(x, 'expired_time', x.timestamp), reverse=True)

        return signals
    
    def generate_live_signals_html(self, filter_type: str = 'all') -> str:
        """Generate HTML for live signals display with countdown timers"""
        signals = self.get_filtered_signals(filter_type)

        # Debug logging
        print(f"🔍 DEBUG: Generating HTML for {len(signals)} signals (filter: {filter_type})")
        print(f"🔍 DEBUG: Total live signals: {len(self.live_signals)}")

        if not signals:
            return """<div style="text-align: center; padding: 2rem; color: rgba(255,255,255,0.6);">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🤖</div>
                <div style="font-size: 1.2rem; font-weight: 600;">AI Bot is Analyzing Markets...</div>
                <div style="font-size: 0.9rem; margin-top: 0.5rem;">Waiting for trading opportunities</div>
            </div>"""
        
        html_content = ""
        timer_data = []
        
        for signal in signals:
            # Calculate time remaining
            now = datetime.now()
            time_remaining = signal.expiry_time - now
            
            if time_remaining.total_seconds() > 0:
                minutes_left = int(time_remaining.total_seconds() / 60)
                seconds_left = int(time_remaining.total_seconds() % 60)
                
                # Determine color and urgency based on time remaining
                if time_remaining.total_seconds() < 30:
                    color = "#ef4444"  # Red - urgent
                    icon = "🚨"
                    urgency_class = "urgent-signal"
                elif time_remaining.total_seconds() < 120:
                    color = "#f59e0b"  # Orange - warning
                    icon = "⚠️"
                    urgency_class = "warning-signal"
                elif time_remaining.total_seconds() < 300:
                    color = "#eab308"  # Yellow - caution
                    icon = "⏰"
                    urgency_class = "caution-signal"
                else:
                    color = "#10b981"  # Green - normal
                    icon = "⏳"
                    urgency_class = "normal-signal"
                
                # Calculate progress percentage
                total_duration = (signal.expiry_time - signal.timestamp).total_seconds()
                elapsed_time = (now - signal.timestamp).total_seconds()
                progress_percent = (elapsed_time / total_duration * 100) if total_duration > 0 else 0
                
                # Generate unique IDs for real-time updates with timestamp
                unique_id = f"{signal.signal_id}_{int(signal.timestamp.timestamp())}"
                card_id = f"signal-card-{unique_id}"
                timer_id = f"timer-{unique_id}"
                progress_id = f"progress-{unique_id}"
                icon_id = f"icon-{unique_id}"
                
                # Store timer data for JavaScript
                timer_data.append({
                    'signal_id': signal.signal_id,
                    'expiry_time': signal.expiry_time.isoformat(),
                    'card_id': card_id,
                    'timer_id': timer_id,
                    'progress_id': progress_id,
                    'icon_id': icon_id,
                    'total_duration': total_duration * 1000  # Convert to milliseconds
                })
                
                # Action-specific styling
                action_color = "#10b981" if signal.action == "CALL" else "#ef4444"
                action_icon = "📞" if signal.action == "CALL" else "📉"
                
                html_content += f"""<div id="{card_id}" class="live-signal-card {urgency_class}" style="background: linear-gradient(135deg, {color}15, {color}08); padding: 1.2rem; border-radius: 12px; margin: 0.8rem 0; border: 2px solid {color}40; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span id="{icon_id}" style="font-size: 1.2rem;">{icon}</span>
                            <div>
                                <div style="color: {action_color}; font-weight: 700; font-size: 1.1rem;">
                                    {action_icon} {signal.asset} • {signal.action}
                                </div>
                                <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">
                                    {signal.strategy_name[:25]}{'...' if len(signal.strategy_name) > 25 else ''}
                                </div>
                            </div>
                        </div>
                        <div id="{timer_id}" style="color: {color}; font-size: 1rem; font-weight: 700; text-align: right;" data-expiry="{signal.expiry_time.isoformat()}" data-total-duration="{total_duration * 1000}">
                            {minutes_left}m {seconds_left}s
                        </div>
                    </div>
                    <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 10px; height: 8px; overflow: hidden; margin: 0.8rem 0;">
                        <div id="{progress_id}" style="height: 100%; width: {progress_percent}%; background: linear-gradient(90deg, {color}, {color}80); border-radius: 10px; transition: width 0.3s ease;"></div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.8rem; margin: 0.8rem 0;">
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Entry Price</div>
                            <div style="color: #3b82f6; font-size: 0.85rem; font-weight: 600;">{signal.entry_price:.5f}</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Confidence</div>
                            <div style="color: #8b5cf6; font-size: 0.85rem; font-weight: 600;">{signal.confidence:.0f}%</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Expiry</div>
                            <div style="color: #f59e0b; font-size: 0.85rem; font-weight: 600;">{signal.expiry_minutes}m</div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.8rem;">
                        <div style="display: flex; align-items: center; gap: 0.3rem;">
                            <div style="color: rgba(255,255,255,0.7); font-size: 0.75rem;">Risk:</div>
                            <div style="color: {'#10b981' if signal.risk_level == 'Low' else '#f59e0b' if signal.risk_level == 'Medium' else '#ef4444'}; font-size: 0.75rem; font-weight: 600;">{signal.risk_level}</div>
                        </div>
                        <div style="color: rgba(255,255,255,0.6); font-size: 0.7rem;">
                            {signal.timestamp.strftime('%H:%M:%S')}
                        </div>
                    </div>
                </div>"""
            
            else:
                # Expired signal
                html_content += f"""<div class="expired-signal-card" style="background: rgba(107, 114, 128, 0.2); padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid #6b728040; opacity: 0.7;">
                    <div style="color: #6b7280; font-weight: 600;">
                        ⏰ {signal.asset} • {signal.action} • EXPIRED
                    </div>
                </div>"""
        
        # Add enhanced JavaScript for real-time countdown updates
        if timer_data:
            timer_data_json = json.dumps(timer_data)
            countdown_js = f"""
            <script>
            // Enhanced real-time countdown system for live signals
            window.liveTimerData = {timer_data_json};

            // Global timer management
            if (!window.liveCountdownIntervals) {{
                window.liveCountdownIntervals = [];
            }}

            // Clear existing intervals to prevent duplicates
            window.liveCountdownIntervals.forEach(interval => {{
                if (interval) clearInterval(interval);
            }});
            window.liveCountdownIntervals = [];

            function updateLiveCountdown(data) {{
                try {{
                    const now = new Date().getTime();
                    const expiryTime = new Date(data.expiry_time).getTime();
                    const timeRemaining = expiryTime - now;

                    if (timeRemaining > 0) {{
                        const minutes = Math.floor(timeRemaining / (1000 * 60));
                        const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

                        // Update timer display with error checking
                        const timerElement = document.getElementById(data.timer_id);
                        if (timerElement) {{
                            timerElement.textContent = minutes + 'm ' + seconds + 's';
                            timerElement.style.color = timeRemaining < 30000 ? '#ef4444' : timeRemaining < 120000 ? '#f59e0b' : '#10b981';
                        }}

                        // Update progress bar with error checking
                        const progressElement = document.getElementById(data.progress_id);
                        if (progressElement) {{
                            const elapsed = data.total_duration - timeRemaining;
                            const progressPercent = Math.min(100, Math.max(0, (elapsed / data.total_duration) * 100));
                            progressElement.style.width = progressPercent + '%';
                        }}

                        // Update urgency styling with error checking
                        const cardElement = document.getElementById(data.card_id);
                        const iconElement = document.getElementById(data.icon_id);

                        if (cardElement && iconElement) {{
                            if (timeRemaining < 30000) {{ // < 30 seconds - URGENT
                                cardElement.style.borderColor = '#ef444460';
                                cardElement.style.background = 'linear-gradient(135deg, #ef444420, #ef444410)';
                                iconElement.textContent = '🚨';
                                cardElement.style.animation = 'pulse 1s infinite';
                            }} else if (timeRemaining < 120000) {{ // < 2 minutes - WARNING
                                cardElement.style.borderColor = '#f59e0b60';
                                cardElement.style.background = 'linear-gradient(135deg, #f59e0b20, #f59e0b10)';
                                iconElement.textContent = '⚠️';
                                cardElement.style.animation = 'none';
                            }} else {{ // NORMAL
                                cardElement.style.borderColor = '#10b98160';
                                cardElement.style.background = 'linear-gradient(135deg, #10b98120, #10b98110)';
                                iconElement.textContent = '⏳';
                                cardElement.style.animation = 'none';
                            }}
                        }}
                    }} else {{
                        // Signal expired - handle gracefully
                        const cardElement = document.getElementById(data.card_id);
                        const timerElement = document.getElementById(data.timer_id);

                        if (timerElement) {{
                            timerElement.textContent = 'EXPIRED';
                            timerElement.style.color = '#6b7280';
                        }}

                        if (cardElement) {{
                            cardElement.style.opacity = '0.6';
                            cardElement.style.borderColor = '#6b728040';
                            cardElement.style.background = 'rgba(107, 114, 128, 0.2)';
                        }}
                    }}
                }} catch (error) {{
                    console.warn('Timer update error:', error);
                }}
            }}

            // Initialize countdown timers with enhanced error handling
            function initializeCountdowns() {{
                if (window.liveTimerData && Array.isArray(window.liveTimerData)) {{
                    window.liveTimerData.forEach(data => {{
                        try {{
                            // Initial update
                            updateLiveCountdown(data);

                            // Set up interval
                            const interval = setInterval(() => {{
                                updateLiveCountdown(data);
                            }}, 1000);

                            window.liveCountdownIntervals.push(interval);
                        }} catch (error) {{
                            console.warn('Timer initialization error:', error);
                        }}
                    }});
                }}
            }}

            // Add CSS animations if not already present
            if (!document.getElementById('live-signals-styles')) {{
                const style = document.createElement('style');
                style.id = 'live-signals-styles';
                style.textContent = `
                    @keyframes pulse {{
                        0% {{ transform: scale(1); box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }}
                        50% {{ transform: scale(1.02); box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }}
                        100% {{ transform: scale(1); box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }}
                    }}
                    .live-signal-card {{
                        transition: all 0.3s ease !important;
                    }}
                `;
                document.head.appendChild(style);
            }}

            // Start timers immediately
            initializeCountdowns();

            // Backup: Re-initialize every 30 seconds to handle Streamlit refreshes
            setInterval(() => {{
                if (window.liveTimerData) {{
                    initializeCountdowns();
                }}
            }}, 30000);

            </script>
            """
            html_content += countdown_js
        
        return html_content

    def generate_expired_signals_html(self, result_filter: str = 'all', date_filter: str = 'all') -> str:
        """Generate HTML for expired signals with WIN/LOSS results"""
        expired_signals = self.get_filtered_expired_signals(result_filter, date_filter)

        # Debug logging
        print(f"🔍 DEBUG: Generating expired signals HTML for {len(expired_signals)} signals")
        print(f"🔍 DEBUG: Filters - Result: {result_filter}, Date: {date_filter}")

        if not expired_signals:
            return """<div style="text-align: center; padding: 2rem; color: rgba(255,255,255,0.6);">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
                <div style="font-size: 1.2rem; font-weight: 600;">No Expired Signals Found</div>
                <div style="font-size: 0.9rem; margin-top: 0.5rem;">Signals will appear here after expiry</div>
            </div>"""

        html_content = ""

        # Statistics header
        total_signals = len(expired_signals)
        win_signals = len([s for s in expired_signals if getattr(s, 'result', 'PENDING') == 'WIN'])
        loss_signals = len([s for s in expired_signals if getattr(s, 'result', 'PENDING') == 'LOSS'])
        pending_signals = len([s for s in expired_signals if getattr(s, 'result', 'PENDING') == 'PENDING'])
        win_rate = (win_signals / (win_signals + loss_signals) * 100) if (win_signals + loss_signals) > 0 else 0

        html_content += f"""<div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 1rem; text-align: center;">
                <div>
                    <div style="color: #3b82f6; font-size: 1.5rem; font-weight: 700;">{total_signals}</div>
                    <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">Total</div>
                </div>
                <div>
                    <div style="color: #10b981; font-size: 1.5rem; font-weight: 700;">{win_signals}</div>
                    <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">WIN</div>
                </div>
                <div>
                    <div style="color: #ef4444; font-size: 1.5rem; font-weight: 700;">{loss_signals}</div>
                    <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">LOSS</div>
                </div>
                <div>
                    <div style="color: #8b5cf6; font-size: 1.5rem; font-weight: 700;">{win_rate:.1f}%</div>
                    <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">Win Rate</div>
                </div>
            </div>
        </div>"""

        # Generate expired signal cards
        for signal in expired_signals:
            result = getattr(signal, 'result', 'PENDING')
            expired_time = getattr(signal, 'expired_time', signal.timestamp)
            exit_price = getattr(signal, 'exit_price', None)
            profit_loss = getattr(signal, 'profit_loss', 0.0)

            # Result-based styling
            if result == 'WIN':
                result_color = "#10b981"
                result_icon = "✅"
                result_bg = "linear-gradient(135deg, #10b98120, #10b98110)"
                border_color = "#10b98160"
            elif result == 'LOSS':
                result_color = "#ef4444"
                result_icon = "❌"
                result_bg = "linear-gradient(135deg, #ef444420, #ef444410)"
                border_color = "#ef444460"
            else:  # PENDING
                result_color = "#f59e0b"
                result_icon = "⏳"
                result_bg = "linear-gradient(135deg, #f59e0b20, #f59e0b10)"
                border_color = "#f59e0b60"

            # Action-specific styling
            action_color = "#10b981" if signal.action == "CALL" else "#ef4444"
            action_icon = "📞" if signal.action == "CALL" else "📉"

            # P&L color
            pl_color = "#10b981" if profit_loss > 0 else "#ef4444" if profit_loss < 0 else "#6b7280"

            html_content += f"""<div style="background: {result_bg}; padding: 1.2rem; border-radius: 12px; margin: 0.8rem 0; border: 2px solid {border_color}; transition: all 0.3s ease;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.2rem;">{result_icon}</span>
                        <div>
                            <div style="color: {action_color}; font-weight: 700; font-size: 1.1rem;">
                                {action_icon} {signal.asset} • {signal.action}
                            </div>
                            <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">
                                {signal.strategy_name[:25]}{'...' if len(signal.strategy_name) > 25 else ''}
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="color: {result_color}; font-size: 1rem; font-weight: 700;">
                            {result}
                        </div>
                        <div style="color: rgba(255,255,255,0.6); font-size: 0.7rem;">
                            {expired_time.strftime('%H:%M:%S')}
                        </div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 0.8rem; margin: 0.8rem 0;">
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Entry Price</div>
                        <div style="color: #3b82f6; font-size: 0.85rem; font-weight: 600;">{signal.entry_price:.5f}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Exit Price</div>
                        <div style="color: #8b5cf6; font-size: 0.85rem; font-weight: 600;">{exit_price:.5f if exit_price else 'N/A'}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">Confidence</div>
                        <div style="color: #f59e0b; font-size: 0.85rem; font-weight: 600;">{signal.confidence:.0f}%</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">P&L</div>
                        <div style="color: {pl_color}; font-size: 0.85rem; font-weight: 600;">
                            {'+' if profit_loss > 0 else ''}{profit_loss:.1f}
                        </div>
                    </div>
                </div>

                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.8rem;">
                    <div style="display: flex; align-items: center; gap: 0.3rem;">
                        <div style="color: rgba(255,255,255,0.7); font-size: 0.75rem;">Expiry:</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem; font-weight: 600;">{signal.expiry_minutes}m</div>
                    </div>
                    <div style="color: rgba(255,255,255,0.6); font-size: 0.7rem;">
                        {expired_time.strftime('%Y-%m-%d')}
                    </div>
                </div>
            </div>"""

        return html_content
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """Get statistics about current live signals"""
        if not self.live_signals:
            return {}
        
        total_signals = len(self.live_signals)
        call_signals = len([s for s in self.live_signals if s.action == 'CALL'])
        put_signals = len([s for s in self.live_signals if s.action == 'PUT'])
        
        avg_confidence = sum(s.confidence for s in self.live_signals) / total_signals
        
        # Time to expiry statistics
        now = datetime.now()
        expiry_times = [(s.expiry_time - now).total_seconds() for s in self.live_signals]
        expiry_times = [t for t in expiry_times if t > 0]  # Only active signals
        
        return {
            'total_signals': total_signals,
            'call_signals': call_signals,
            'put_signals': put_signals,
            'avg_confidence': avg_confidence,
            'active_signals': len(expiry_times),
            'avg_time_to_expiry': sum(expiry_times) / len(expiry_times) if expiry_times else 0,
            'urgent_signals': len([t for t in expiry_times if t < 30]),  # < 30 seconds
            'warning_signals': len([t for t in expiry_times if 30 <= t < 120])  # 30s - 2min
        }

    def get_expired_signal_statistics(self, date_filter: str = 'all') -> Dict[str, Any]:
        """Get statistics about expired signals"""
        expired_signals = self.get_filtered_expired_signals('all', date_filter)

        if not expired_signals:
            return {}

        total_signals = len(expired_signals)
        win_signals = len([s for s in expired_signals if getattr(s, 'result', 'PENDING') == 'WIN'])
        loss_signals = len([s for s in expired_signals if getattr(s, 'result', 'PENDING') == 'LOSS'])
        pending_signals = len([s for s in expired_signals if getattr(s, 'result', 'PENDING') == 'PENDING'])

        win_rate = (win_signals / (win_signals + loss_signals) * 100) if (win_signals + loss_signals) > 0 else 0

        # Calculate average confidence for different result types
        win_confidences = [s.confidence for s in expired_signals if getattr(s, 'result', 'PENDING') == 'WIN']
        loss_confidences = [s.confidence for s in expired_signals if getattr(s, 'result', 'PENDING') == 'LOSS']

        avg_win_confidence = sum(win_confidences) / len(win_confidences) if win_confidences else 0
        avg_loss_confidence = sum(loss_confidences) / len(loss_confidences) if loss_confidences else 0

        # Asset performance
        asset_stats = {}
        for signal in expired_signals:
            asset = signal.asset
            result = getattr(signal, 'result', 'PENDING')

            if asset not in asset_stats:
                asset_stats[asset] = {'total': 0, 'wins': 0, 'losses': 0}

            asset_stats[asset]['total'] += 1
            if result == 'WIN':
                asset_stats[asset]['wins'] += 1
            elif result == 'LOSS':
                asset_stats[asset]['losses'] += 1

        # Calculate win rates per asset
        for asset in asset_stats:
            total = asset_stats[asset]['wins'] + asset_stats[asset]['losses']
            asset_stats[asset]['win_rate'] = (asset_stats[asset]['wins'] / total * 100) if total > 0 else 0

        return {
            'total_signals': total_signals,
            'win_signals': win_signals,
            'loss_signals': loss_signals,
            'pending_signals': pending_signals,
            'win_rate': win_rate,
            'avg_win_confidence': avg_win_confidence,
            'avg_loss_confidence': avg_loss_confidence,
            'asset_performance': asset_stats,
            'best_asset': max(asset_stats.items(), key=lambda x: x[1]['win_rate'])[0] if asset_stats else None,
            'worst_asset': min(asset_stats.items(), key=lambda x: x[1]['win_rate'])[0] if asset_stats else None
        }
