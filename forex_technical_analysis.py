# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - AI Strategy-Based Trading Bot for PocketOption
# =============================================================================
import streamlit as st
import streamlit.components.v1 as components
import time
import numpy as np
from datetime import datetime
from tradingview_ta import TA_Handler, Interval
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Import new strategy engine components
try:
    from strategy_engine import StrategyEngine
    from indicator_calculator import IndicatorCalculator
    from strategy_templates import StrategyTemplates
    from signal_generator import SignalGenerator
    from signal_tracker import SignalTracker
    from performance_evaluator import PerformanceEvaluator
    from performance_database import PerformanceDatabase
    from traditional_signal_tracker import TraditionalSignalTracker
    from automated_trading_bot import AutomatedTradingBot
    from live_signal_manager import LiveSignalManager
    from ai_learning_optimizer import AILearningOptimizer
    STRATEGY_ENGINE_AVAILABLE = True
except ImportError as e:
    print(f"Strategy engine components not available: {e}")
    STRATEGY_ENGINE_AVAILABLE = False

# --- SESSION STATE INITIALIZATION ---
if 'all_results' not in st.session_state:
    st.session_state.all_results = {}
if 'last_strong_signals' not in st.session_state:
    st.session_state.last_strong_signals = set()
if 'signal_deduplication_cache' not in st.session_state:
    st.session_state.signal_deduplication_cache = {}
if 'last_signal_generation_time' not in st.session_state:
    st.session_state.last_signal_generation_time = {}
if 'selected_symbol' not in st.session_state:
    st.session_state.selected_symbol = None
if 'scan_index' not in st.session_state:
    st.session_state.scan_index = 0
if 'scan_iteration' not in st.session_state:
    st.session_state.scan_iteration = 0
if 'last_scores' not in st.session_state:
    st.session_state.last_scores = {}
if 'circuit_breaker_active' not in st.session_state:
    st.session_state.circuit_breaker_active = False
if 'circuit_breaker_until' not in st.session_state:
    st.session_state.circuit_breaker_until = None
if 'consecutive_failures' not in st.session_state:
    st.session_state.consecutive_failures = 0
if 'signal_timestamps' not in st.session_state:
    st.session_state.signal_timestamps = {}
if 'signal_hunter_mode' not in st.session_state:
    st.session_state.signal_hunter_mode = True
if 'chart_loading_status' not in st.session_state:
    st.session_state.chart_loading_status = None
# --- NEW: Chart Analysis Mode Controls ---
if 'chart_analysis_mode' not in st.session_state:
    st.session_state.chart_analysis_mode = False
if 'auto_refresh_paused' not in st.session_state:
    st.session_state.auto_refresh_paused = False
if 'last_manual_update' not in st.session_state:
    st.session_state.last_manual_update = None
if 'chart_session_start' not in st.session_state:
    st.session_state.chart_session_start = None
if 'show_strong_signals_only' not in st.session_state:
    st.session_state.show_strong_signals_only = True  # Default to strong signals only
if 'binary_options_mode' not in st.session_state:
    st.session_state.binary_options_mode = True  # Default to binary options mode
if 'signal_sensitivity' not in st.session_state:
    st.session_state.signal_sensitivity = "medium"  # Default sensitivity
if 'pair_update_timestamps' not in st.session_state:
    st.session_state.pair_update_timestamps = {}  # Track when each pair was last updated
if 'pair_update_status' not in st.session_state:
    st.session_state.pair_update_status = {}  # Track update status for each pair
if 'live_dashboard_metrics' not in st.session_state:
    st.session_state.live_dashboard_metrics = {}  # Live dashboard data
if 'progressive_update_queue' not in st.session_state:
    st.session_state.progressive_update_queue = []  # Queue for progressive updates
if 'api_health_status' not in st.session_state:
    st.session_state.api_health_status = "healthy"  # API health tracking

# --- NEW: Automated Trading Bot Session State ---
if 'trading_bot_active' not in st.session_state:
    st.session_state.trading_bot_active = False
if 'trading_bot_thread' not in st.session_state:
    st.session_state.trading_bot_thread = None
if 'live_signals_queue' not in st.session_state:
    st.session_state.live_signals_queue = []
if 'bot_analysis_results' not in st.session_state:
    st.session_state.bot_analysis_results = {}
if 'bot_performance_metrics' not in st.session_state:
    st.session_state.bot_performance_metrics = {}
if 'ai_learning_data' not in st.session_state:
    st.session_state.ai_learning_data = {}
if 'market_analysis_cache' not in st.session_state:
    st.session_state.market_analysis_cache = {}
if 'live_chart_data' not in st.session_state:
    st.session_state.live_chart_data = {}
if 'bot_last_analysis_time' not in st.session_state:
    st.session_state.bot_last_analysis_time = {}
if 'selected_trading_strategy' not in st.session_state:
    st.session_state.selected_trading_strategy = None
if 'bot_signal_history' not in st.session_state:
    st.session_state.bot_signal_history = []
if 'real_time_updates_enabled' not in st.session_state:
    st.session_state.real_time_updates_enabled = True

# --- NEW: Strategy Engine Session State ---
if 'strategy_engine_initialized' not in st.session_state:
    st.session_state.strategy_engine_initialized = False
if 'active_strategies' not in st.session_state:
    st.session_state.active_strategies = {}
if 'custom_strategy_text' not in st.session_state:
    st.session_state.custom_strategy_text = ""
if 'selected_template' not in st.session_state:
    st.session_state.selected_template = None
if 'strategy_signals' not in st.session_state:
    st.session_state.strategy_signals = []
if 'strategy_mode_enabled' not in st.session_state:
    st.session_state.strategy_mode_enabled = False
if 'show_traditional_signals' not in st.session_state:
    st.session_state.show_traditional_signals = True  # Default to showing traditional signals alongside AI strategy signals
if 'performance_tracking_enabled' not in st.session_state:
    st.session_state.performance_tracking_enabled = True
if 'traditional_signal_tracker' not in st.session_state:
    st.session_state.traditional_signal_tracker = None
if 'traditional_tracking_enabled' not in st.session_state:
    st.session_state.traditional_tracking_enabled = True
if 'performance_database' not in st.session_state:
    st.session_state.performance_database = None
if 'signal_tracker' not in st.session_state:
    st.session_state.signal_tracker = None
if 'performance_evaluator' not in st.session_state:
    st.session_state.performance_evaluator = None
if 'last_api_data' not in st.session_state:
    st.session_state.last_api_data = {}

# --- INITIALIZE STRATEGY ENGINE COMPONENTS ---
if STRATEGY_ENGINE_AVAILABLE and not st.session_state.strategy_engine_initialized:
    try:
        st.session_state.strategy_engine = StrategyEngine()
        st.session_state.indicator_calculator = IndicatorCalculator()
        st.session_state.strategy_templates = StrategyTemplates()
        st.session_state.signal_generator = SignalGenerator()

        # Initialize performance tracking components
        st.session_state.performance_database = PerformanceDatabase()
        st.session_state.signal_tracker = SignalTracker(st.session_state.performance_database)
        st.session_state.performance_evaluator = PerformanceEvaluator(st.session_state.performance_database)

        # Initialize traditional signal tracker
        st.session_state.traditional_signal_tracker = TraditionalSignalTracker()

        # Initialize automated trading bot components
        st.session_state.automated_trading_bot = AutomatedTradingBot(
            strategy_engine=st.session_state.strategy_engine,
            signal_generator=st.session_state.signal_generator,
            performance_database=st.session_state.performance_database,
            signal_tracker=st.session_state.signal_tracker,
            performance_evaluator=st.session_state.performance_evaluator
        )

        st.session_state.live_signal_manager = LiveSignalManager()
        st.session_state.ai_learning_optimizer = AILearningOptimizer(
            performance_database=st.session_state.performance_database,
            performance_evaluator=st.session_state.performance_evaluator
        )

        # Start automatic signal tracking
        if st.session_state.performance_tracking_enabled:
            st.session_state.signal_tracker.start_tracking()

        # Start traditional signal tracking
        if st.session_state.traditional_tracking_enabled:
            st.session_state.traditional_signal_tracker.start_tracking()

        st.session_state.strategy_engine_initialized = True
        print("✅ Strategy Engine v2.2 with Automated Trading Bot initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Strategy Engine: {e}")
        st.session_state.strategy_engine_initialized = False

# --- HELPER FUNCTIONS FOR PERFORMANCE DASHBOARD ---

def generate_pending_signals_html(pending_signals):
    """Generate HTML for pending signals with countdown timers and progress bars."""
    html_content = ""

    for signal in pending_signals:
        signal_id, asset, signal_type, entry_price, expiry_time, confidence, strategy_name, timestamp = signal

        try:
            from datetime import datetime
            expiry_dt = datetime.fromisoformat(expiry_time)
            signal_dt = datetime.fromisoformat(timestamp)
            now = datetime.now()

            time_remaining = expiry_dt - now
            total_duration = expiry_dt - signal_dt

            if time_remaining.total_seconds() > 0:
                minutes_left = int(time_remaining.total_seconds() / 60)
                seconds_left = int(time_remaining.total_seconds() % 60)
                time_str = f"{minutes_left}m {seconds_left}s remaining"

                # Determine color based on time remaining
                if time_remaining.total_seconds() > 5 * 60:  # > 5 minutes
                    status_color = "#10b981"  # Green
                    status_icon = "⏳"
                    urgency_class = ""
                elif time_remaining.total_seconds() > 2 * 60:  # 2-5 minutes
                    status_color = "#f59e0b"  # Yellow
                    status_icon = "⚠️"
                    urgency_class = ""
                else:  # < 2 minutes
                    status_color = "#ef4444"  # Red
                    status_icon = "🚨" if time_remaining.total_seconds() < 30 else "⚠️"
                    urgency_class = "urgent-signal" if time_remaining.total_seconds() < 30 else ""

                # Calculate progress percentage
                elapsed_time = total_duration.total_seconds() - time_remaining.total_seconds()
                progress_percent = (elapsed_time / total_duration.total_seconds()) * 100 if total_duration.total_seconds() > 0 else 0

            else:
                time_str = "EXPIRED"
                status_color = "#6b7280"
                status_icon = "⏰"
                urgency_class = "expired-signal"
                progress_percent = 100

            # Generate HTML for this signal
            html_content += f"""
            <div class="pending-signal-card {urgency_class}"
                 data-expiry-time="{expiry_time}"
                 data-total-time="{total_duration.total_seconds() * 1000}"
                 style="background: rgba(245, 158, 11, 0.1); padding: 0.8rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid {status_color}40; transition: all 0.3s ease;">

                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                    <div style="color: {status_color}; font-weight: 700; font-size: 1rem;">
                        <span class="status-icon">{status_icon}</span> {asset} • {signal_type}
                    </div>
                    <div class="countdown-text" style="color: rgba(255,255,255,0.9); font-size: 0.8rem; font-weight: 600;">
                        {time_str}
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container" style="background-color: rgba(255, 255, 255, 0.1); border-radius: 10px; height: 8px; overflow: hidden; margin: 0.5rem 0;">
                    <div class="progress-fill" style="height: 100%; width: {progress_percent}%; background-color: {status_color}; border-radius: 10px; transition: width 0.3s ease, background-color 0.3s ease;"></div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin: 0.5rem 0;">
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; font-weight: 600;">Entry Price</div>
                        <div style="color: #3b82f6; font-size: 0.8rem;">{entry_price:.5f}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; font-weight: 600;">Confidence</div>
                        <div style="color: #8b5cf6; font-size: 0.8rem;">{confidence:.0f}%</div>
                    </div>
                </div>

                <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem; text-align: center;">
                    {strategy_name[:30]}{'...' if len(strategy_name) > 30 else ''}
                </div>
            </div>
            """

        except Exception as e:
            # Fallback for any parsing errors
            html_content += f"""
            <div style="background: rgba(107, 114, 128, 0.1); padding: 0.8rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid #6b728040;">
                <div style="color: #6b7280; font-weight: 700; font-size: 1rem;">
                    ❓ {asset} • {signal_type} • Error loading timer
                </div>
            </div>
            """

    return html_content

# --- UNIFIED TRADINGVIEW-TA ENGINE ---
@st.cache_data(ttl=120)  # Increased cache to 120 seconds to reduce API calls
def get_unified_tradingview_data(symbol, timeframe_str="5m"):
    """Get unified TradingView data for both signals and charts with circuit breaker"""
    import time
    import random
    from datetime import datetime, timedelta

    # Check circuit breaker status
    if st.session_state.circuit_breaker_active:
        if datetime.now() < st.session_state.circuit_breaker_until:
            print(f"CIRCUIT BREAKER: API calls suspended until {st.session_state.circuit_breaker_until.strftime('%H:%M:%S')}")
            # Return cached data if available, otherwise return demo data
            cache_key = f"{symbol}_{timeframe_str}"
            if cache_key in st.session_state.get('chart_cache', {}):
                return st.session_state.chart_cache[cache_key]
            return get_demo_data(symbol, timeframe_str)
        else:
            # Reset circuit breaker
            st.session_state.circuit_breaker_active = False
            st.session_state.consecutive_failures = 0
            print(f"CIRCUIT BREAKER: Reset - resuming API calls")

    # Map timeframe strings to TradingView intervals
    interval_map = {
        "5m": Interval.INTERVAL_5_MINUTES,
        "15m": Interval.INTERVAL_15_MINUTES,
        "30m": Interval.INTERVAL_30_MINUTES,
        "1h": Interval.INTERVAL_1_HOUR
    }

    tv_interval = interval_map.get(timeframe_str, Interval.INTERVAL_5_MINUTES)
    max_retries = 2  # Reduced retries to minimize API hammering
    base_delay = 3   # Increased base delay

    for attempt in range(max_retries):
        try:
            handler = TA_Handler(
                symbol=symbol,
                screener="forex",
                exchange="FX_IDC",
                interval=tv_interval
            )
            analysis = handler.get_analysis()

            # Extract signal summary
            buy_signals = analysis.summary['BUY']
            sell_signals = analysis.summary['SELL']
            score = (buy_signals - sell_signals) * 2  # Scale for better range

            # Extract technical indicators for charting
            indicators = analysis.indicators

            # Create chart dataframe with available data
            chart_data = create_chart_dataframe(indicators, symbol)

            # Create summary data
            summary_data = {
                'score': score,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'indicators': indicators
            }

            # Log API response details (only in debug mode)
            api_debug_log(symbol, buy_signals, sell_signals, score)

            # Reset failure counter on successful API call
            st.session_state.consecutive_failures = 0

            # Store detailed API data for transparency
            if 'last_api_data' not in st.session_state:
                st.session_state.last_api_data = {}
            st.session_state.last_api_data[symbol] = {
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'score': score,
                'timestamp': datetime.now(),
                'timeframe': timeframe_str,
                'total_indicators': buy_signals + sell_signals + analysis.summary.get('NEUTRAL', 0)
            }

            # Cache the results
            cache_key = f"{symbol}_{timeframe_str}"
            if 'chart_cache' not in st.session_state:
                st.session_state.chart_cache = {}
            st.session_state.chart_cache[cache_key] = (summary_data, chart_data)

            # Store score for backward compatibility
            st.session_state.last_scores[symbol] = score

            return summary_data, chart_data

        except Exception as e:
            error_msg = str(e).lower()
            debug_log(f"API Error - {symbol}: Attempt {attempt + 1}/{max_retries} - {e}", "ERROR")

            # Handle rate limiting (429 errors) - activate circuit breaker
            if "429" in error_msg or "rate limit" in error_msg:
                st.session_state.consecutive_failures += 1
                print(f"Rate limit failure #{st.session_state.consecutive_failures} for {symbol}")

                # Activate circuit breaker after 3 consecutive failures
                if st.session_state.consecutive_failures >= 3:
                    st.session_state.circuit_breaker_active = True
                    st.session_state.circuit_breaker_until = datetime.now() + timedelta(minutes=10)
                    print(f"CIRCUIT BREAKER ACTIVATED: API calls suspended for 10 minutes until {st.session_state.circuit_breaker_until.strftime('%H:%M:%S')}")

                if attempt < max_retries - 1:
                    # Much longer delays for rate limiting
                    delay = base_delay * (3 ** attempt) + random.uniform(2, 5)
                    print(f"Rate limited for {symbol}, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    continue
                else:
                    print(f"Rate limit exceeded for {symbol} after {max_retries} attempts")
            else:
                print(f"Error analyzing {symbol}: {e}")

            # Return last known score if available, otherwise return 0
            if symbol in st.session_state.last_scores:
                cached_score = st.session_state.last_scores[symbol]
                print(f"Using cached score for {symbol}: {cached_score}")
                return cached_score

            break

    # Enhanced fallback with demo mode
    if st.session_state.circuit_breaker_active:
        # Demo mode: provide sample signals to maintain app functionality
        demo_scores = {
            "EURUSD": 15, "GBPUSD": -12, "USDJPY": 8, "AUDUSD": -18,
            "USDCAD": 22, "USDCHF": -5, "NZDUSD": 11, "EURJPY": -25,
            "EURGBP": 3, "EURAUD": 19, "EURCAD": -8, "EURCHF": 14,
            "GBPJPY": -21, "AUDJPY": 7, "CADJPY": -15, "CHFJPY": 12,
            "GBPAUD": 16, "GBPCAD": -9, "GBPCHF": 6, "AUDCAD": -13,
            "AUDCHF": 18, "AUDNZD": -4, "CADCHF": 10
        }
        demo_score = demo_scores.get(symbol, 0)
        print(f"DEMO MODE: {symbol} = {demo_score} (simulated signal)")
        return demo_score

    debug_log(f"Fallback - {symbol}: All retries failed, using demo data", "WARN")
    return get_demo_data(symbol, timeframe_str)

def create_chart_dataframe(indicators, symbol):
    """Create a chart dataframe from TradingView indicators"""
    try:
        # Extract available data from indicators
        # Note: TradingView-TA provides limited historical data
        # This creates a minimal dataframe for demonstration

        # Generate sample OHLC data based on current price if available
        current_price = indicators.get('close', 1.0000)  # Default forex price

        # Create 50 periods of sample data
        periods = 50
        dates = pd.date_range(end=pd.Timestamp.now(), periods=periods, freq='5min')

        # Generate realistic OHLC data with some volatility
        np.random.seed(hash(symbol) % 1000)  # Consistent seed per symbol
        price_changes = np.random.normal(0, 0.001, periods)
        prices = [current_price]

        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        # Create OHLC from price series
        opens = prices[:-1]
        closes = prices[1:]
        highs = [max(o, c) * (1 + abs(np.random.normal(0, 0.0005))) for o, c in zip(opens, closes)]
        lows = [min(o, c) * (1 - abs(np.random.normal(0, 0.0005))) for o, c in zip(opens, closes)]

        # Create technical indicators
        rsi_values = np.random.uniform(30, 70, periods-1)  # RSI between 30-70
        macd_values = np.random.normal(0, 0.001, periods-1)
        signal_values = np.random.normal(0, 0.0008, periods-1)

        # Create Bollinger Bands
        bb_upper = [c * 1.002 for c in closes]
        bb_lower = [c * 0.998 for c in closes]
        bb_middle = closes

        df = pd.DataFrame({
            'datetime': dates[1:],
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'rsi': rsi_values,
            'macd': macd_values,
            'signal': signal_values,
            'bb_upper': bb_upper,
            'bb_lower': bb_lower,
            'bb_middle': bb_middle
        })

        return df

    except Exception as e:
        print(f"Error creating chart dataframe for {symbol}: {e}")
        return pd.DataFrame()  # Return empty dataframe on error

def play_alert_sound():
    """Play alert sound for new signals (placeholder function)"""
    # This is a placeholder function for alert sounds
    # In a real implementation, you could use libraries like pygame or playsound
    pass

def convert_to_binary_signal(category, score, symbol):
    """Convert forex signal to binary options format for PocketOption"""
    if symbol not in BINARY_OPTIONS_CONFIG["pocketoption_pairs"]:
        return None

    signal_info = BINARY_OPTIONS_CONFIG["signal_mapping"].get(category)
    if not signal_info or signal_info["action"] == "WAIT":
        return None

    # Determine optimal expiry based on signal strength
    if abs(score) >= 25:
        expiry = "5m"  # Strong signals get shorter expiry
    elif abs(score) >= 20:
        expiry = "5m"
    elif abs(score) >= 15:
        expiry = "15m"
    else:
        expiry = "15m"  # Weaker signals get longer expiry

    return {
        "symbol": symbol,
        "action": signal_info["action"],  # CALL or PUT
        "confidence": signal_info["confidence"],
        "score": score,
        "expiry": expiry,
        "expiry_minutes": BINARY_OPTIONS_CONFIG["expiry_times"][expiry],
        "platform": "PocketOption",
        "signal_type": "Binary Options"
    }

def get_binary_signal_color(action, confidence):
    """Get color scheme for binary options signals"""
    if action == "CALL":
        return "#10b981" if confidence == "HIGH" else "#3b82f6"  # Green/Blue for CALL
    elif action == "PUT":
        return "#ef4444" if confidence == "HIGH" else "#f59e0b"  # Red/Orange for PUT
    else:
        return "#6b7280"  # Gray for WAIT

def get_binary_signal_icon(action, confidence):
    """Get icon for binary options signals"""
    if action == "CALL":
        return "📞" if confidence == "HIGH" else "📈"
    elif action == "PUT":
        return "📉" if confidence == "HIGH" else "⬇️"
    else:
        return "⏸️"

def update_pair_status(symbol, status, timestamp=None):
    """Update the status and timestamp for a specific pair"""
    if timestamp is None:
        timestamp = datetime.now()

    st.session_state.pair_update_timestamps[symbol] = timestamp
    st.session_state.pair_update_status[symbol] = status

def get_pair_freshness(symbol):
    """Get how fresh the data is for a specific pair"""
    if symbol not in st.session_state.pair_update_timestamps:
        return "never", "🔴"

    last_update = st.session_state.pair_update_timestamps[symbol]
    time_diff = (datetime.now() - last_update).total_seconds()

    if time_diff < 30:
        return f"{int(time_diff)}s ago", "🟢"
    elif time_diff < 120:
        return f"{int(time_diff/60)}m ago", "🟡"
    else:
        return f"{int(time_diff/60)}m ago", "🔴"

def calculate_live_dashboard_metrics():
    """Calculate real-time dashboard metrics"""
    metrics = {
        'total_pairs': 0,
        'active_pairs': 0,
        'strong_signals': 0,
        'bullish_pairs': 0,
        'bearish_pairs': 0,
        'neutral_pairs': 0,
        'avg_score': 0,
        'market_sentiment': 'Neutral',
        'api_health': st.session_state.api_health_status,
        'last_update': datetime.now()
    }

    all_scores = []
    for category, pairs in st.session_state.all_results.items():
        for symbol, score in pairs:
            metrics['total_pairs'] += 1
            all_scores.append(score)

            # Check if pair has recent data
            if symbol in st.session_state.pair_update_timestamps:
                last_update = st.session_state.pair_update_timestamps[symbol]
                if (datetime.now() - last_update).total_seconds() < 120:
                    metrics['active_pairs'] += 1

            # Categorize signals
            if abs(score) >= 20:
                metrics['strong_signals'] += 1

            if score > 5:
                metrics['bullish_pairs'] += 1
            elif score < -5:
                metrics['bearish_pairs'] += 1
            else:
                metrics['neutral_pairs'] += 1

    if all_scores:
        metrics['avg_score'] = sum(all_scores) / len(all_scores)

        # Determine market sentiment
        if metrics['avg_score'] > 10:
            metrics['market_sentiment'] = 'Strongly Bullish'
        elif metrics['avg_score'] > 5:
            metrics['market_sentiment'] = 'Bullish'
        elif metrics['avg_score'] < -10:
            metrics['market_sentiment'] = 'Strongly Bearish'
        elif metrics['avg_score'] < -5:
            metrics['market_sentiment'] = 'Bearish'
        else:
            metrics['market_sentiment'] = 'Neutral'

    st.session_state.live_dashboard_metrics = metrics
    return metrics

def get_current_price(symbol):
    """Get current price for a symbol from the price manager or fallback."""
    try:
        if hasattr(st.session_state, 'price_manager') and st.session_state.price_manager:
            return st.session_state.price_manager.get_price(symbol)
        else:
            # Fallback to demo prices
            demo_prices = {
                'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 150.25,
                'AUDUSD': 0.6650, 'USDCAD': 1.3580, 'USDCHF': 0.8850,
                'NZDUSD': 0.6150, 'EURJPY': 162.50, 'GBPJPY': 189.75,
                'AUDJPY': 99.85, 'EURGBP': 0.8580, 'EURAUD': 1.6320
            }
            return demo_prices.get(symbol, 1.0000)
    except Exception as e:
        print(f"Error getting current price for {symbol}: {e}")
        return None

def get_demo_data(symbol, timeframe_str):
    """Generate demo data for circuit breaker mode"""
    # Demo scores for realistic signal distribution
    demo_scores = {
        "EURUSD": 15, "GBPUSD": -12, "USDJPY": 8, "AUDUSD": -18,
        "USDCAD": 22, "USDCHF": -5, "NZDUSD": 11, "EURJPY": -25,
        "EURGBP": 3, "EURAUD": 19, "EURCAD": -8, "EURCHF": 14,
        "GBPJPY": -21, "AUDJPY": 7, "CADJPY": -15, "CHFJPY": 12,
        "GBPAUD": 16, "GBPCAD": -9, "GBPCHF": 6, "AUDCAD": -13,
        "AUDCHF": 18, "AUDNZD": -4, "CADCHF": 10
    }

    demo_score = demo_scores.get(symbol, 0)

    # Create demo summary data
    summary_data = {
        'score': demo_score,
        'buy_signals': max(0, demo_score + 10),
        'sell_signals': max(0, -demo_score + 10),
        'indicators': {}
    }

    # Create demo chart data
    chart_data = create_chart_dataframe({}, symbol)

    print(f"DEMO MODE: {symbol} = {demo_score} (simulated signal)")
    return summary_data, chart_data

def create_plotly_chart(df, symbol, timeframe):
    """Create a 3-panel Plotly chart with candlesticks, MACD, and RSI"""
    if df.empty:
        # Return empty figure if no data
        fig = go.Figure()
        fig.add_annotation(text="No chart data available",
                          xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        return fig

    # Create subplots: 3 rows (Candlestick + BB, MACD, RSI)
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=(f'{symbol} Price & Bollinger Bands', 'MACD', 'RSI'),
        row_heights=[0.6, 0.2, 0.2]
    )

    # Panel 1: Candlestick chart with Bollinger Bands
    fig.add_trace(
        go.Candlestick(
            x=df['datetime'],
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name=symbol,
            increasing_line_color='#00ff88',
            decreasing_line_color='#ff4444'
        ),
        row=1, col=1
    )

    # Bollinger Bands
    fig.add_trace(
        go.Scatter(x=df['datetime'], y=df['bb_upper'],
                  line=dict(color='rgba(173,216,230,0.8)', width=1),
                  name='BB Upper', showlegend=False),
        row=1, col=1
    )
    fig.add_trace(
        go.Scatter(x=df['datetime'], y=df['bb_lower'],
                  line=dict(color='rgba(173,216,230,0.8)', width=1),
                  name='BB Lower', fill='tonexty', fillcolor='rgba(173,216,230,0.1)',
                  showlegend=False),
        row=1, col=1
    )
    fig.add_trace(
        go.Scatter(x=df['datetime'], y=df['bb_middle'],
                  line=dict(color='rgba(255,255,255,0.6)', width=1),
                  name='BB Middle', showlegend=False),
        row=1, col=1
    )

    # Panel 2: MACD
    fig.add_trace(
        go.Scatter(x=df['datetime'], y=df['macd'],
                  line=dict(color='#00ff88', width=2),
                  name='MACD'),
        row=2, col=1
    )
    fig.add_trace(
        go.Scatter(x=df['datetime'], y=df['signal'],
                  line=dict(color='#ff4444', width=2),
                  name='Signal'),
        row=2, col=1
    )

    # Panel 3: RSI
    fig.add_trace(
        go.Scatter(x=df['datetime'], y=df['rsi'],
                  line=dict(color='#ffaa00', width=2),
                  name='RSI'),
        row=3, col=1
    )

    # Add RSI overbought/oversold lines
    fig.add_hline(y=70, line_dash="dash", line_color="red",
                  annotation_text="Overbought", row=3, col=1)
    fig.add_hline(y=30, line_dash="dash", line_color="green",
                  annotation_text="Oversold", row=3, col=1)

    # Update layout for dark theme
    fig.update_layout(
        title=f"{symbol} Technical Analysis ({timeframe})",
        template="plotly_dark",
        height=600,
        showlegend=True,
        legend=dict(x=0, y=1, bgcolor='rgba(0,0,0,0.5)'),
        margin=dict(l=50, r=50, t=80, b=50)
    )

    # Update y-axes
    fig.update_yaxes(title_text="Price", row=1, col=1)
    fig.update_yaxes(title_text="MACD", row=2, col=1)
    fig.update_yaxes(title_text="RSI", row=3, col=1, range=[0, 100])

    # Update x-axis
    fig.update_xaxes(title_text="Time", row=3, col=1)

    return fig

def calculate_signal_expiry(score, volatility_factor=1.0):
    """Calculate dynamic signal expiration based on strength and volatility"""
    from datetime import datetime, timedelta

    # Base expiration times in minutes
    base_times = {
        "very_strong": 45,  # |score| >= 30
        "strong": 60,       # |score| >= 20
        "weak": 90          # |score| >= 10
    }

    # Determine signal strength
    abs_score = abs(score)
    if abs_score >= 30:
        base_time = base_times["very_strong"]
        strength_factor = 0.5  # Very strong signals expire faster
    elif abs_score >= 20:
        base_time = base_times["strong"]
        strength_factor = 1.0  # Normal expiration
    else:
        base_time = base_times["weak"]
        strength_factor = 1.5  # Weak signals last longer

    # Apply volatility adjustment
    # High volatility (>1.0) = shorter expiry, Low volatility (<1.0) = longer expiry
    volatility_adjustment = 0.8 if volatility_factor > 1.2 else (1.3 if volatility_factor < 0.8 else 1.0)

    # Calculate final expiry time
    final_minutes = int(base_time * strength_factor * volatility_adjustment)
    expiry_time = datetime.now() + timedelta(minutes=final_minutes)

    return expiry_time, final_minutes



def play_alert_sound():
    """Play audio alert for new strong signals"""
    sound_html = """
    <script>
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    } catch(e) { console.log('Audio not supported'); }
    </script>
    """
    components.html(sound_html, height=0)

# --- STREAMLIT UI CONFIGURATION ---
st.set_page_config(
    page_title="AIFXHunter Pro v2.2 - AI Strategy-Based Trading Bot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# --- ENHANCED PROFESSIONAL CSS STYLING ---
st.markdown("""
<style>
    /* Import Professional Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

    /* Global Styling */
    .main {
        padding-top: 0.5rem;
        background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
        min-height: 100vh;
    }

    /* Enhanced Header */
    .main-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 30%, #06b6d4 70%, #10b981 100%);
        padding: 2.5rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.4), 0 0 0 1px rgba(59, 130, 246, 0.3);
        position: relative;
        overflow: hidden;
    }

    .main-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .main-title {
        font-family: 'Inter', sans-serif;
        font-size: 3rem;
        font-weight: 800;
        color: white;
        margin: 0;
        text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
        position: relative;
        z-index: 1;
        background: linear-gradient(45deg, #ffffff, #e0f2fe, #ffffff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .main-subtitle {
        font-family: 'Inter', sans-serif;
        font-size: 1.2rem;
        color: rgba(255,255,255,0.9);
        margin: 0.8rem 0 0 0;
        font-weight: 500;
        position: relative;
        z-index: 1;
    }

    /* Enhanced Signal Cards */
    .signal-card {
        background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
        border-radius: 16px;
        padding: 1.8rem;
        margin: 0.8rem 0;
        border: 1px solid rgba(100, 116, 139, 0.3);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 1px rgba(59, 130, 246, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .signal-card:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.4), 0 0 0 1px rgba(59, 130, 246, 0.4);
        border-color: rgba(59, 130, 246, 0.6);
    }

    .signal-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--signal-color);
        border-radius: 16px 16px 0 0;
    }

    .signal-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Enhanced Signal Types */
    .strong-buy {
        --signal-color: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
        border-left: 3px solid #10b981;
    }
    .weak-buy {
        --signal-color: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
        border-left: 3px solid #3b82f6;
    }
    .neutral {
        --signal-color: linear-gradient(90deg, #6b7280, #9ca3af, #d1d5db);
        border-left: 3px solid #6b7280;
    }
    .weak-sell {
        --signal-color: linear-gradient(90deg, #f59e0b, #fbbf24, #fcd34d);
        border-left: 3px solid #f59e0b;
    }
    .strong-sell {
        --signal-color: linear-gradient(90deg, #ef4444, #f87171, #fca5a5);
        border-left: 3px solid #ef4444;
    }

    /* Professional Buttons */
    .stButton > button {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .stButton > button:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* Chart Button Styling */
    .chart-button {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.6rem 1.2rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .chart-button:hover {
        background: linear-gradient(135deg, #047857 0%, #059669 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4) !important;
    }

    /* Enhanced Form Controls */
    .stSelectbox > div > div {
        background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
        border: 1px solid rgba(100, 116, 139, 0.4);
        border-radius: 12px;
        color: white;
        font-family: 'Inter', sans-serif;
        transition: all 0.3s ease;
    }

    .stSelectbox > div > div:hover {
        border-color: rgba(59, 130, 246, 0.6);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .stCheckbox > label {
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        color: rgba(255,255,255,0.9);
    }

    /* Enhanced Metrics and Stats */
    .metric-container {
        background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
        border-radius: 16px;
        padding: 1.5rem;
        border: 1px solid rgba(100, 116, 139, 0.3);
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    .metric-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(0,0,0,0.3);
        border-color: rgba(59, 130, 246, 0.4);
    }

    .metric-value {
        font-family: 'JetBrains Mono', monospace;
        font-size: 2.2rem;
        font-weight: 700;
        color: #3b82f6;
        margin: 0;
        text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
        position: relative;
        z-index: 1;
    }

    .metric-label {
        font-family: 'Inter', sans-serif;
        font-size: 0.95rem;
        color: rgba(255,255,255,0.8);
        margin: 0.5rem 0 0 0;
        font-weight: 500;
        position: relative;
        z-index: 1;
    }

    /* Enhanced Sidebar Styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        border-right: 1px solid rgba(100, 116, 139, 0.2);
    }

    /* Enhanced Status Indicators */
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 10px;
        animation: pulse 2s infinite;
        box-shadow: 0 0 10px currentColor;
    }

    .status-active {
        background-color: #10b981;
        box-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
    }
    .status-warning {
        background-color: #f59e0b;
        box-shadow: 0 0 15px rgba(245, 158, 11, 0.6);
    }
    .status-error {
        background-color: #ef4444;
        box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
    }

    @keyframes pulse {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.1); }
        100% { opacity: 1; transform: scale(1); }
    }

    /* Enhanced Chart Container */
    .chart-container {
        background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(100, 116, 139, 0.3);
        box-shadow: 0 20px 40px rgba(0,0,0,0.3), 0 0 0 1px rgba(59, 130, 246, 0.1);
        margin: 1.5rem 0;
        position: relative;
        overflow: hidden;
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Enhanced Loading Animation */
    .loading-spinner {
        border: 4px solid rgba(59, 130, 246, 0.2);
        border-radius: 50%;
        border-top: 4px solid #3b82f6;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 2rem auto;
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Progress Bars */
    .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(100, 116, 139, 0.3);
        border-radius: 4px;
        overflow: hidden;
        margin: 0.5rem 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #06b6d4);
        border-radius: 4px;
        transition: width 0.3s ease;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .main-title { font-size: 2.5rem; }
        .signal-card { padding: 1.2rem; }
        .main-header { padding: 2rem; }
    }

    @media (max-width: 768px) {
        .main-title { font-size: 2rem; }
        .signal-card { padding: 1rem; }
        .main-header { padding: 1.5rem; }
        .metric-value { font-size: 1.8rem; }
    }

    @media (max-width: 480px) {
        .main-title { font-size: 1.5rem; }
        .main-subtitle { font-size: 1rem; }
        .signal-card { padding: 0.8rem; }
        .main-header { padding: 1rem; }
    }
</style>
""", unsafe_allow_html=True)

# --- PROFESSIONAL HEADER ---
st.markdown("""
<div class="main-header">
    <h1 class="main-title">📈 AIFXHunter Pro v2.1</h1>
    <p class="main-subtitle">Professional Real-Time Forex Signal Analysis & TradingView Integration</p>
</div>
""", unsafe_allow_html=True)

# --- ENHANCED REAL-TIME SCANNING CONFIGURATION ---
SCAN_INTERVAL_SECONDS = 8   # Optimized for better real-time performance
PAIRS_PER_SCAN = 3          # Increased for faster coverage of all pairs
PROGRESSIVE_UPDATE_INTERVAL = 2  # Update individual pairs every 2 seconds
MAX_CONCURRENT_UPDATES = 5   # Maximum pairs updating simultaneously

# --- BINARY OPTIONS CONFIGURATION FOR POCKETOPTION ---
BINARY_OPTIONS_CONFIG = {
    "expiry_times": {
        "1m": 1,    # 1-minute expiry
        "5m": 5,    # 5-minute expiry
        "15m": 15   # 15-minute expiry
    },
    "signal_sensitivity": {
        "high": 15,    # Minimum score for high sensitivity (more signals)
        "medium": 20,  # Minimum score for medium sensitivity
        "low": 25      # Minimum score for low sensitivity (fewer, stronger signals)
    },
    "pocketoption_pairs": [
        "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF",
        "NZDUSD", "EURJPY", "GBPJPY", "AUDJPY", "EURGBP", "EURAUD"
    ],
    "signal_mapping": {
        "Strong Buy": {"action": "CALL", "confidence": "HIGH", "min_expiry": "5m"},
        "Weak Buy": {"action": "CALL", "confidence": "MEDIUM", "min_expiry": "15m"},
        "Strong Sell": {"action": "PUT", "confidence": "HIGH", "min_expiry": "5m"},
        "Weak Sell": {"action": "PUT", "confidence": "MEDIUM", "min_expiry": "15m"},
        "Neutral": {"action": "WAIT", "confidence": "LOW", "min_expiry": "N/A"}
    }
}



# Forex pairs watchlist (23 major pairs)
watchlist = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD",
    "EURJPY", "EURGBP", "EURAUD", "EURCAD", "EURCHF",
    "GBPJPY", "AUDJPY", "CADJPY", "CHFJPY",
    "GBPAUD", "GBPCAD", "GBPCHF",
    "AUDCAD", "AUDCHF", "AUDNZD", "CADCHF"
]

# === COMPLETE UI LAYOUT REDESIGN ===
# Create the new single-page dashboard layout

# TOP SECTION: Enhanced Header with Strategy Engine Status
strategy_status = "🤖 AI Strategy Engine Active" if st.session_state.strategy_engine_initialized else "⚠️ Strategy Engine Loading"
st.markdown(f"""
<div style="background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
    <div style="color: white; font-weight: 700; font-size: 1.2rem; text-align: center;">🤖 AIFXHunter Pro v2.2 - AI Strategy-Based Trading Bot</div>
    <div style="color: rgba(255,255,255,0.8); font-size: 0.9rem; text-align: center; margin-top: 0.5rem;">{strategy_status} • PocketOption Binary Options • Real-time Strategy Evaluation</div>
</div>
""", unsafe_allow_html=True)

# This section is now replaced by the clickable pairs above

# Make Major Currency Pairs clickable to open charts
st.markdown("### 📊 Major Currency Pairs (Click to Open Chart)")

# Create clickable pairs grid
pairs_per_row = 6
for i in range(0, len(watchlist[:18]), pairs_per_row):  # Show first 18 pairs
    row_pairs = watchlist[i:i+pairs_per_row]
    cols = st.columns(len(row_pairs))
    for j, pair in enumerate(row_pairs):
        with cols[j]:
            # Get current signal for this pair
            current_signal = "Neutral"
            current_score = 0
            signal_color = "gray"

            for category, pairs in st.session_state.all_results.items():
                for symbol, score in pairs:
                    if symbol == pair:
                        current_signal = category
                        current_score = score
                        if "Strong Buy" in category:
                            signal_color = "green"
                        elif "Weak Buy" in category:
                            signal_color = "blue"
                        elif "Strong Sell" in category:
                            signal_color = "red"
                        elif "Weak Sell" in category:
                            signal_color = "orange"
                        break

            # Clickable pair button that opens chart
            button_text = f"{pair}\n{current_score:.0f}\n{current_signal[:6]}"
            if st.session_state.selected_symbol == pair:
                button_text = f"✅ {pair}\n{current_score:.0f}\nACTIVE"

            if st.button(
                button_text,
                key=f"pair_chart_{pair}",
                use_container_width=True,
                help=f"Click to open {pair} chart",
                type="primary" if st.session_state.selected_symbol == pair else "secondary"
            ):
                # Set the selected symbol and force chart analysis mode
                st.session_state.selected_symbol = pair
                st.session_state.chart_analysis_mode = True
                st.session_state.chart_session_start = datetime.now()
                st.session_state.auto_refresh_paused = True
                st.success(f"🎯 Opening {pair} chart in center panel...")
                st.rerun()

# MAIN LAYOUT: Three-column layout (Left Sidebar | Center Content | Right Panel)
left_sidebar, center_content, right_panel = st.columns([1, 2, 1])

# === LEFT SIDEBAR: Control Center ===
with left_sidebar:
    st.markdown("""
    <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
        <div style="color: white; font-weight: 700; font-size: 1.1rem; text-align: center;">⚙️ Control Center</div>
    </div>
    """, unsafe_allow_html=True)

    # Automated Trading Bot Controls
    st.markdown("### 🤖 AI Trading Bot")

    if STRATEGY_ENGINE_AVAILABLE and st.session_state.strategy_engine_initialized:
        # Bot status
        bot_running = st.session_state.automated_trading_bot.is_bot_running() if hasattr(st.session_state, 'automated_trading_bot') else False

        if bot_running:
            st.success("🟢 **AI Bot is ACTIVE**")
            if st.button("🛑 Stop Trading Bot", type="secondary", use_container_width=True):
                st.session_state.automated_trading_bot.stop_bot()
                st.success("🛑 Trading Bot stopped")
                st.rerun()
        else:
            st.info("🔴 **AI Bot is STOPPED**")

            # Strategy selection for bot
            available_strategies = list(st.session_state.strategy_templates.get_all_templates().keys()) if hasattr(st.session_state, 'strategy_templates') else []
            selected_strategy = st.selectbox(
                "Select Trading Strategy",
                ["Auto_AI_Strategy"] + available_strategies,
                help="Choose the AI strategy for automated trading"
            )

            if st.button("🚀 Start Trading Bot", type="primary", use_container_width=True):
                success = st.session_state.automated_trading_bot.start_bot(selected_strategy)
                if success:
                    st.session_state.selected_trading_strategy = selected_strategy
                    st.success(f"🚀 Trading Bot started with {selected_strategy}")
                    st.rerun()
                else:
                    st.error("❌ Failed to start Trading Bot")
    else:
        st.warning("⚠️ Strategy Engine not available")

    # Binary Options Mode Toggle
    st.markdown("### 📞 Trading Mode")
    binary_options_mode = st.checkbox(
        "🎯 PocketOption Binary Mode",
        value=st.session_state.binary_options_mode,
        help="Enable binary options signals optimized for PocketOption platform"
    )
    st.session_state.binary_options_mode = binary_options_mode

    if binary_options_mode:
        st.markdown("**Binary Options Settings:**")

        # Signal Sensitivity
        signal_sensitivity = st.selectbox(
            "Signal Sensitivity",
            ["high", "medium", "low"],
            index=1,  # Default to medium
            help="High: More signals (≥15), Medium: Balanced (≥20), Low: Fewer, stronger signals (≥25)"
        )
        st.session_state.signal_sensitivity = signal_sensitivity

        # Strong Signals Only Filter
        show_strong_only = st.checkbox(
            "🔥 Strong Signals Only",
            value=st.session_state.show_strong_signals_only,
            help="Only display Strong Buy/Strong Sell signals (filters out weak signals)"
        )
        st.session_state.show_strong_signals_only = show_strong_only

        st.markdown(f"""
        <div style="background: rgba(59, 130, 246, 0.1); padding: 0.8rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid rgba(59, 130, 246, 0.2);">
            <div style="color: #3b82f6; font-weight: 600; font-size: 0.85rem; margin-bottom: 0.3rem;">📞 PocketOption Mode Active</div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem;">• CALL/PUT signals instead of Buy/Sell</div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem;">• Binary expiry times: 5m, 15m</div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem;">• Sensitivity: {signal_sensitivity.title()}</div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.75rem;">• Filter: {"Strong Only" if show_strong_only else "All Signals"}</div>
        </div>
        """, unsafe_allow_html=True)

    # Keep existing sidebar controls but make them more compact
    st.markdown("### 🕐 Timeframe")
    timeframe = st.selectbox(
        "Analysis Period",
        ["5m", "15m", "30m", "1h"],
        index=0,
        help="Select timeframe for technical analysis"
    )

    st.markdown("### 🎯 Signal Mode")
    signal_hunter_mode = st.checkbox(
        "Signal Hunter Mode",
        value=st.session_state.signal_hunter_mode,
        help="Only alert for NEW Strong signals"
    )
    st.session_state.signal_hunter_mode = signal_hunter_mode

    # Debug Mode Toggle (for development and troubleshooting)
    debug_mode = st.checkbox(
        "🔧 Debug Mode",
        value=st.session_state.get('debug_mode', False),
        help="Enable detailed console logging for troubleshooting (developers only)"
    )
    st.session_state.debug_mode = debug_mode

    st.markdown("### 📊 Chart Analysis")
    auto_refresh_paused = st.checkbox(
        "Pause Auto-Refresh",
        value=st.session_state.auto_refresh_paused,
        help="Pause for uninterrupted chart analysis"
    )
    st.session_state.auto_refresh_paused = auto_refresh_paused

    # Chart analysis mode status
    if st.session_state.selected_symbol:
        st.session_state.chart_analysis_mode = True
        if not st.session_state.chart_session_start:
            st.session_state.chart_session_start = datetime.now()

        session_duration = datetime.now() - st.session_state.chart_session_start
        minutes = int(session_duration.total_seconds() / 60)
        seconds = int(session_duration.total_seconds() % 60)

        st.success(f"📈 Chart Analysis Active\n{st.session_state.selected_symbol} • {minutes}m {seconds}s")
    else:
        st.session_state.chart_analysis_mode = False
        st.session_state.chart_session_start = None

    # Manual controls
    if st.session_state.auto_refresh_paused or st.session_state.chart_analysis_mode:
        if st.button("🔄 Update Signals", use_container_width=True):
            st.session_state.last_manual_update = datetime.now()
            st.rerun()

    # AI Strategy Builder Section
    if st.session_state.strategy_engine_initialized:
        st.markdown("### 🤖 AI Strategy Builder")

        # Strategy Mode Toggle
        strategy_mode = st.checkbox(
            "🎯 Enable AI Strategy Mode",
            value=st.session_state.strategy_mode_enabled,
            help="Enable AI-powered strategy-based signal generation"
        )
        st.session_state.strategy_mode_enabled = strategy_mode

        # Traditional Signals Toggle (only show when strategy mode is enabled)
        if st.session_state.strategy_mode_enabled:
            show_traditional_signals = st.checkbox(
                "📊 Show Traditional Signals",
                value=st.session_state.get('show_traditional_signals', True),
                help="Show traditional technical analysis signals alongside AI strategy signals"
            )
            st.session_state.show_traditional_signals = show_traditional_signals

        if strategy_mode:
            # Strategy Input Method
            strategy_input_method = st.radio(
                "Strategy Input Method",
                ["📝 Custom Strategy", "📋 Template Library"],
                help="Choose how to define your trading strategy"
            )

            if strategy_input_method == "📝 Custom Strategy":
                # Custom Strategy Text Input
                st.markdown("**Enter Your Strategy:**")
                custom_strategy = st.text_area(
                    "Strategy Description",
                    value=st.session_state.custom_strategy_text,
                    placeholder="Example: If RSI is below 30 and MACD shows bullish crossover, then generate CALL signal",
                    help="Describe your trading strategy in plain English",
                    height=100
                )
                st.session_state.custom_strategy_text = custom_strategy

                # Validate Strategy
                if custom_strategy and hasattr(st.session_state, 'strategy_engine'):
                    validation = st.session_state.strategy_engine.validate_strategy(custom_strategy)
                    if validation['valid']:
                        st.success("✅ Strategy is valid!")
                    else:
                        st.error("❌ Strategy validation failed:")
                        for error in validation['errors']:
                            st.error(f"• {error}")
                        for suggestion in validation['suggestions']:
                            st.info(f"💡 {suggestion}")

            else:  # Template Library
                # Strategy Template Selection
                if hasattr(st.session_state, 'strategy_templates'):
                    templates = st.session_state.strategy_templates.get_all_templates()
                    template_options = {f"{tid}: {template['name']}": tid for tid, template in templates.items()}

                    selected_template_display = st.selectbox(
                        "Select Strategy Template",
                        options=list(template_options.keys()),
                        help="Choose from pre-defined strategy templates"
                    )

                    if selected_template_display:
                        selected_template_id = template_options[selected_template_display]
                        st.session_state.selected_template = selected_template_id

                        # Show template details
                        template = templates[selected_template_id]
                        st.markdown(f"**{template['name']}**")
                        st.markdown(f"*{template['description']}*")
                        st.markdown(f"**Category:** {template['category']}")
                        st.markdown(f"**Difficulty:** {template['difficulty']}")
                        st.markdown(f"**Success Rate:** {template['success_rate']}")

                        with st.expander("📋 Strategy Details"):
                            st.markdown(f"**Strategy Text:** {template['strategy_text']}")
                            st.markdown(f"**Best Pairs:** {', '.join(template['best_pairs'])}")
                            st.markdown(f"**Notes:** {template['notes']}")

                            # Show real-time performance if available
                            if hasattr(st.session_state, 'performance_evaluator') and st.session_state.performance_evaluator:
                                try:
                                    perf_eval = st.session_state.performance_evaluator.evaluate_strategy_performance(
                                        selected_template_id, days=7
                                    )
                                    if perf_eval.get('status') == 'analyzed':
                                        metrics = perf_eval.get('metrics', {})
                                        win_rate = metrics.get('win_rate', 0)
                                        evaluated = metrics.get('evaluated_signals', 0)

                                        if evaluated >= 3:
                                            st.markdown(f"**📊 Live Performance (7 days):**")
                                            st.markdown(f"- Win Rate: {win_rate:.1f}%")
                                            st.markdown(f"- Signals Evaluated: {evaluated}")
                                            st.markdown(f"- Performance Level: {perf_eval.get('performance_level', 'unknown').title()}")
                                        else:
                                            st.markdown("**📊 Live Performance:** Insufficient data (need 3+ signals)")
                                except Exception as e:
                                    st.markdown("**📊 Live Performance:** Data loading...")

    # Performance Analytics Section
    if st.session_state.strategy_engine_initialized and hasattr(st.session_state, 'performance_evaluator'):
        st.markdown("### 📊 Performance Analytics")

        # Performance tracking toggle
        tracking_enabled = st.checkbox(
            "📈 Enable Performance Tracking",
            value=st.session_state.performance_tracking_enabled,
            help="Automatically track signal outcomes and performance"
        )
        st.session_state.performance_tracking_enabled = tracking_enabled

        # Traditional signal tracking toggle
        traditional_tracking = st.checkbox(
            "📊 Track Traditional Signals",
            value=st.session_state.traditional_tracking_enabled,
            help="Enable AI-powered tracking and optimization of traditional technical signals"
        )
        st.session_state.traditional_tracking_enabled = traditional_tracking

        # Traditional signal performance display
        if traditional_tracking and hasattr(st.session_state, 'traditional_signal_tracker'):
            try:
                trad_stats = st.session_state.traditional_signal_tracker.get_performance_stats(days=7)
                if trad_stats['overall']['total'] > 0:
                    st.markdown("**📊 Traditional Signals (7 days):**")
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Win Rate", f"{trad_stats['overall']['win_rate']:.1f}%")
                    with col2:
                        st.metric("Signals", trad_stats['overall']['total'])

                    # AI Recommendations
                    recommendations = st.session_state.traditional_signal_tracker.get_ai_recommendations()
                    if recommendations:
                        st.markdown("**🤖 AI Insights:**")
                        for rec in recommendations[:2]:  # Show top 2 recommendations
                            if rec['type'] == 'WIN_PATTERN':
                                st.success(f"✅ {rec['data']['recommendation']}")
                            elif rec['type'] == 'LOSS_PATTERN':
                                st.warning(f"⚠️ {rec['data']['recommendation']}")

                    # Show recent traditional signals with timestamps
                    recent_signals = st.session_state.traditional_signal_tracker.get_recent_signals(limit=5)
                    if recent_signals:
                        st.markdown("**📊 Recent Traditional Signals:**")
                        for signal in recent_signals:
                            timestamp = signal.get('timestamp', 'Unknown')
                            if isinstance(timestamp, str):
                                try:
                                    # Parse timestamp and format it
                                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                    time_str = dt.strftime('%H:%M:%S')
                                except:
                                    time_str = timestamp
                            else:
                                time_str = str(timestamp)

                            result = signal.get('result', 'PENDING')
                            asset = signal.get('asset', 'Unknown')
                            signal_type = signal.get('signal_type', 'Unknown')

                            if result == 'WIN':
                                st.success(f"✅ {asset} {signal_type} @ {time_str}")
                            elif result == 'LOSS':
                                st.error(f"❌ {asset} {signal_type} @ {time_str}")
                            else:
                                st.info(f"⏳ {asset} {signal_type} @ {time_str} (Pending)")
                else:
                    st.info("📊 No traditional signal data yet. Signals will be tracked automatically!")
            except Exception as e:
                st.error(f"Error loading traditional signal data: {e}")

        if tracking_enabled:
            # Quick performance overview
            try:
                rolling_24h = st.session_state.performance_database.get_rolling_performance(hours=24)
                if rolling_24h and rolling_24h.get('total_signals', 0) > 0:
                    st.markdown("**📈 Last 24 Hours:**")
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Win Rate", f"{rolling_24h.get('win_rate', 0):.1f}%")
                    with col2:
                        st.metric("Signals", rolling_24h.get('total_signals', 0))
                else:
                    st.info("📊 No performance data yet. Generate some signals to see analytics!")

                # Force evaluation button
                if st.button("🔄 Update Performance", key=f"update_perf_{hash('sidebar')}", help="Force check for expired signals"):
                    if st.session_state.signal_tracker:
                        st.session_state.signal_tracker.force_evaluation_check()
                        st.success("Performance updated!")
                        st.rerun()

            except Exception as e:
                st.error(f"Error loading performance data: {e}")

    # Technical Indicators
    st.markdown("### 📈 Indicators")
    if 'selected_indicators' not in st.session_state:
        st.session_state.selected_indicators = ["RSI", "MACD", "Bollinger Bands", "EMA (21)", "SMA (50)"]

    available_indicators = {
        "RSI": "RSI@tv-basicstudies",
        "MACD": "MACD@tv-basicstudies",
        "Bollinger Bands": "BB@tv-basicstudies",
        "EMA (21)": "EMA@tv-basicstudies",
        "SMA (50)": "SMA@tv-basicstudies",
        "Stochastic RSI": "StochasticRSI@tv-basicstudies",
        "Volume": "Volume@tv-basicstudies"
    }

    selected_indicators = st.multiselect(
        "Chart Indicators",
        options=list(available_indicators.keys()),
        default=st.session_state.selected_indicators,
        help="Select technical indicators for charts"
    )
    st.session_state.selected_indicators = selected_indicators

    # System Status
    st.markdown("### 📡 System Status")
    if st.session_state.circuit_breaker_active:
        st.error("🛡️ Circuit Breaker Active")
    elif st.session_state.consecutive_failures > 0:
        st.warning(f"⚠️ API Issues: {st.session_state.consecutive_failures}/3")
    else:
        st.success("✅ System Operational")

# === CENTER CONTENT: Charts and Strategy ===
with center_content:
    st.markdown("""
    <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
        <div style="color: white; font-weight: 700; font-size: 1.1rem; text-align: center;">📊 Professional Trading Charts & Strategy</div>
    </div>
    """, unsafe_allow_html=True)

    # Set default symbol if none selected
    if not st.session_state.selected_symbol:
        st.session_state.selected_symbol = "EURUSD"  # Default to EURUSD
        st.session_state.chart_analysis_mode = True
        if not st.session_state.chart_session_start:
            st.session_state.chart_session_start = datetime.now()

    # Display current chart
    if st.session_state.selected_symbol:
        # Enhanced Chart Container with selected symbol indicator
        st.markdown(f"""
        <div class="chart-container">
            <div style="background: linear-gradient(135deg, #059669 0%, #10b981 100%); padding: 0.8rem; border-radius: 8px; margin-bottom: 1rem; text-align: center; animation: pulse 2s infinite;">
                <div style="color: white; font-weight: 700; font-size: 1rem;">📊 Now Loading: {st.session_state.selected_symbol}</div>
                <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem;">Professional TradingView Chart</div>
                <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem; margin-top: 0.3rem;">Chart will update automatically when symbol changes</div>
            </div>
            <style>
                @keyframes pulse {{
                    0% {{ opacity: 1; }}
                    50% {{ opacity: 0.8; }}
                    100% {{ opacity: 1; }}
                }}
            </style>
        """, unsafe_allow_html=True)

        # Show enhanced loading status
        with st.spinner(f"🔄 Loading {st.session_state.selected_symbol} Professional TradingView Chart..."):
            # TradingView widget configuration
            tv_symbol = f"FX:{st.session_state.selected_symbol}"
            timeframe_map = {"5m": "5", "15m": "15", "30m": "30", "1h": "60"}
            tv_interval = timeframe_map.get(timeframe, "5")

            # Generate unique container ID to prevent conflicts
            import hashlib
            unique_id = hashlib.md5(f"{st.session_state.selected_symbol}_{timeframe}_{int(time.time()/10)}".encode()).hexdigest()[:8]
            container_id = f"tv_chart_{unique_id}"

            # Build indicators list based on user selection
            available_indicators = {
                "RSI": "RSI@tv-basicstudies",
                "MACD": "MACD@tv-basicstudies",
                "Bollinger Bands": "BB@tv-basicstudies",
                "EMA (21)": "EMA@tv-basicstudies",
                "SMA (50)": "SMA@tv-basicstudies",
                "Stochastic RSI": "StochasticRSI@tv-basicstudies",
                "Volume": "Volume@tv-basicstudies"
            }

            # Get selected indicators
            selected_studies = []
            if hasattr(st.session_state, 'selected_indicators'):
                for indicator in st.session_state.selected_indicators:
                    if indicator in available_indicators:
                        selected_studies.append(available_indicators[indicator])

            # Default indicators if none selected
            if not selected_studies:
                selected_studies = ["RSI@tv-basicstudies", "MACD@tv-basicstudies", "BB@tv-basicstudies"]

            # Convert to JavaScript array format
            studies_js = '", "'.join(selected_studies)
            studies_js = f'["{studies_js}"]' if studies_js else '[]'

            # Enhanced TradingView HTML with Dynamic Technical Indicators
            tradingview_html = f"""
            <div id="{container_id}" style="height:500px; width:100%; background-color:#1e1e1e; border-radius:8px;"></div>
            <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
            <script type="text/javascript">
                setTimeout(function() {{
                    try {{
                        new TradingView.widget({{
                            "width": "100%",
                            "height": 500,
                            "symbol": "{tv_symbol}",
                            "interval": "{tv_interval}",
                            "timezone": "Etc/UTC",
                            "theme": "dark",
                            "style": "1",
                            "locale": "en",
                            "toolbar_bg": "#1e293b",
                            "enable_publishing": false,
                            "allow_symbol_change": true,
                            "hide_side_toolbar": false,
                            "container_id": "{container_id}",
                            "autosize": true,
                            "studies": {studies_js},
                            "studies_overrides": {{
                                "RSI.length": 14,
                                "MACD.fast length": 12,
                                "MACD.slow length": 26,
                                "MACD.signal": 9,
                                "BB.length": 20,
                                "BB.stdDev": 2,
                                "EMA.length": 21,
                                "SMA.length": 50,
                                "StochasticRSI.length": 14
                            }},
                            "overrides": {{
                                "paneProperties.background": "#1e293b",
                                "paneProperties.vertGridProperties.color": "#334155",
                                "paneProperties.horzGridProperties.color": "#334155",
                                "symbolWatermarkProperties.transparency": 90,
                                "scalesProperties.textColor": "#94a3b8",
                                "mainSeriesProperties.candleStyle.upColor": "#10b981",
                                "mainSeriesProperties.candleStyle.downColor": "#ef4444",
                                "mainSeriesProperties.candleStyle.borderUpColor": "#10b981",
                                "mainSeriesProperties.candleStyle.borderDownColor": "#ef4444",
                                "mainSeriesProperties.candleStyle.wickUpColor": "#10b981",
                                "mainSeriesProperties.candleStyle.wickDownColor": "#ef4444"
                            }},
                            "show_popup_button": true,
                            "popup_width": "1000",
                            "popup_height": "650",
                            "save_image": false,
                            "details": true,
                            "hotlist": true,
                            "calendar": true,
                            "studies_access": {{
                                "type": "black",
                                "tools": [
                                    {{"name": "Trend Line"}},
                                    {{"name": "Horizontal Line"}},
                                    {{"name": "Vertical Line"}},
                                    {{"name": "Rectangle"}},
                                    {{"name": "Fibonacci Retracement"}}
                                ]
                            }}
                        }});
                        console.log('TradingView chart with indicators loaded for {tv_symbol}');
                    }} catch(e) {{
                        console.error('TradingView error:', e);
                        document.getElementById('{container_id}').innerHTML = '<div style="color:white;text-align:center;padding:50px;">Chart loading failed. Please refresh the page.</div>';
                    }}
                }}, 1000);
            </script>
            """

            # Display the TradingView chart
            components.html(tradingview_html, height=520)

        # Close chart container div
        st.markdown("</div>", unsafe_allow_html=True)

        # Enhanced Chart Controls with Analysis Mode Indicators
        st.markdown("""
        <div style="background: linear-gradient(145deg, #1e293b 0%, #334155 100%); padding: 1.5rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(100, 116, 139, 0.3);">
        """, unsafe_allow_html=True)

        # Chart Analysis Mode Status
        if st.session_state.chart_analysis_mode:
            session_duration = datetime.now() - st.session_state.chart_session_start
            minutes = int(session_duration.total_seconds() / 60)
            seconds = int(session_duration.total_seconds() % 60)

            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #059669 0%, #10b981 100%); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid rgba(16, 185, 129, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between; color: white;">
                    <div style="display: flex; align-items: center;">
                        <div style="font-size: 1.2rem; margin-right: 0.8rem;">📊</div>
                        <div>
                            <div style="font-weight: 700; font-size: 1rem;">Chart Analysis Mode Active</div>
                            <div style="color: rgba(255,255,255,0.9); font-size: 0.85rem;">Auto-refresh paused for uninterrupted analysis</div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="background: rgba(255,255,255,0.2); padding: 0.3rem 0.6rem; border-radius: 6px; font-weight: 600; font-size: 0.8rem;">
                            {minutes}m {seconds}s
                        </div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # Chart info and controls (using HTML grid instead of columns)
        # Get indicator count
        indicator_count = len(st.session_state.selected_indicators) if hasattr(st.session_state, 'selected_indicators') else 3

        # Get current signal info for this symbol
        current_signal = "Neutral"
        current_score = 0
        for category, pairs in st.session_state.all_results.items():
            for symbol, score in pairs:
                if symbol == st.session_state.selected_symbol:
                    current_signal = category
                    current_score = score
                    break

        signal_color = "#10b981" if "Buy" in current_signal else "#ef4444" if "Sell" in current_signal else "#6b7280"

        st.markdown(f"""
        <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 1rem; margin: 1rem 0; align-items: center;">
            <div style="color: rgba(255,255,255,0.8); font-size: 0.9rem;">
                <strong>Chart Info:</strong> {st.session_state.selected_symbol} • {timeframe} • {indicator_count} Indicators
            </div>
            <div style="color: {signal_color}; font-size: 0.9rem; font-weight: 600;">
                Current Signal: {current_signal} ({current_score:.1f})
            </div>
            <div></div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("🗑️ Close Chart", key="clear_chart_btn", use_container_width=True):
            st.session_state.selected_symbol = None
            st.session_state.chart_analysis_mode = False
            st.session_state.chart_session_start = None
            st.rerun()

        # Additional Chart Controls (using individual buttons instead of columns)
        st.markdown("**Chart Controls:**")

        if st.button("🔄 Refresh Chart Data", key="refresh_chart_data", use_container_width=True):
            # Force refresh chart data without full page reload
            st.rerun()

        pause_text = "▶️ Resume Auto-Refresh" if st.session_state.auto_refresh_paused else "⏸️ Pause Auto-Refresh"
        if st.button(pause_text, key="toggle_auto_refresh", use_container_width=True):
            st.session_state.auto_refresh_paused = not st.session_state.auto_refresh_paused
            st.rerun()

        if st.button("📊 New Chart", key="new_chart_analysis", use_container_width=True):
            # Reset chart session timer
            st.session_state.chart_session_start = datetime.now()
            st.rerun()

        st.markdown("</div>", unsafe_allow_html=True)

    # Dynamic Strategy Information Panel
    # Determine current strategy based on settings
    if st.session_state.strategy_mode_enabled and st.session_state.strategy_engine_initialized:
        # AI Strategy Mode is active
        if st.session_state.custom_strategy_text:
            strategy_name = "🤖 Custom AI Strategy"
            strategy_description = st.session_state.custom_strategy_text[:100] + "..." if len(st.session_state.custom_strategy_text) > 100 else st.session_state.custom_strategy_text
            strategy_source = "Custom user-defined strategy"
            strategy_method = "AI-powered natural language processing"
        elif st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
            template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
            if template:
                strategy_name = f"🤖 {template['name']}"
                strategy_description = template['description']
                strategy_source = f"Template Library ({template['category']})"
                strategy_method = "Pre-built AI strategy template"
            else:
                strategy_name = "🤖 AI Strategy (Template Loading)"
                strategy_description = "Loading template strategy..."
                strategy_source = "Template Library"
                strategy_method = "Pre-built AI strategy template"
        else:
            strategy_name = "🤖 AI Strategy (Not Configured)"
            strategy_description = "Please configure your AI strategy in the left panel"
            strategy_source = "AI Strategy Engine"
            strategy_method = "Awaiting strategy configuration"

        strategy_scoring = "AI confidence scoring (0-100%)"
        strategy_expiry = "AI-calculated based on strategy conditions"
    else:
        # Traditional Multi-Indicator Strategy
        strategy_name = "📈 Multi-Indicator Consensus Strategy"
        strategy_description = "Traditional technical analysis using multiple indicators"
        strategy_source = "TradingView Technical Analysis (17+ indicators)"
        strategy_method = "Bullish vs Bearish indicator consensus"
        strategy_scoring = "(Buy Signals - Sell Signals) × 2"
        strategy_expiry = "Dynamic based on signal strength"

    # Add strategy status indicator
    strategy_status_color = "#10b981" if st.session_state.strategy_mode_enabled else "#6b7280"
    strategy_status_text = "ACTIVE" if st.session_state.strategy_mode_enabled else "TRADITIONAL"
    current_time_str = datetime.now().strftime('%H:%M:%S')

    # Display the dynamic strategy panel
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%); padding: 1.5rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(59, 130, 246, 0.2);">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
            <div style="color: #3b82f6; font-weight: 700; font-size: 1.2rem;">🎯 Current Trading Strategy</div>
            <div style="background: {strategy_status_color}; color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                {strategy_status_text} • {current_time_str}
            </div>
        </div>
        <div style="color: rgba(255,255,255,0.9); font-size: 0.95rem; line-height: 1.6;">
            <strong>{strategy_name}</strong><br>
            • <strong>Description:</strong> {strategy_description}<br>
            • <strong>Data Source:</strong> {strategy_source}<br>
            • <strong>Methodology:</strong> {strategy_method}<br>
            • <strong>Scoring:</strong> {strategy_scoring}<br>
            • <strong>Timeframe:</strong> {timeframe} analysis<br>
            • <strong>Expiry:</strong> {strategy_expiry}
        </div>
    </div>
    """, unsafe_allow_html=True)

# === RIGHT PANEL: Live Signals & Bot Status ===
with right_panel:
    st.markdown("""
    <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
        <div style="color: white; font-weight: 700; font-size: 1.1rem; text-align: center;">📊 Live Signals</div>
    </div>
    """, unsafe_allow_html=True)

    # Live Trading Bot Status
    if STRATEGY_ENGINE_AVAILABLE and st.session_state.strategy_engine_initialized:
        bot_running = st.session_state.automated_trading_bot.is_bot_running() if hasattr(st.session_state, 'automated_trading_bot') else False

        if bot_running:
            # Get bot performance metrics
            bot_metrics = st.session_state.automated_trading_bot.get_performance_metrics()

            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid rgba(16, 185, 129, 0.3);">
                <div style="color: white; font-weight: 700; font-size: 1rem; text-align: center;">🤖 AI Bot ACTIVE</div>
                <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; text-align: center; margin-top: 0.3rem;">
                    Strategy: {st.session_state.get('selected_trading_strategy', 'Auto_AI_Strategy')}
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-top: 0.8rem;">
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Signals Today</div>
                        <div style="color: white; font-size: 0.9rem; font-weight: 600;">{bot_metrics.get('total_signals_today', 0)}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Win Rate 24h</div>
                        <div style="color: white; font-size: 0.9rem; font-weight: 600;">{bot_metrics.get('win_rate_24h', 0):.1f}%</div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            # Live signals from bot
            if hasattr(st.session_state, 'live_signal_manager'):
                live_signals = st.session_state.live_signal_manager.get_filtered_signals('all')

                if live_signals:
                    st.markdown("### 🎯 Live Bot Signals")

                    # Signal filter
                    signal_filter = st.selectbox(
                        "Filter Signals",
                        ["all", "high_confidence", "call_signals", "put_signals"],
                        format_func=lambda x: st.session_state.live_signal_manager.signal_categories.get(x, x),
                        key="live_signal_filter"
                    )

                    # Generate and display live signals HTML
                    live_signals_html = st.session_state.live_signal_manager.generate_live_signals_html(signal_filter)
                    st.markdown(live_signals_html, unsafe_allow_html=True)

                    # Signal statistics
                    stats = st.session_state.live_signal_manager.get_signal_statistics()
                    if stats:
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Active Signals", stats.get('active_signals', 0))
                        with col2:
                            st.metric("Avg Confidence", f"{stats.get('avg_confidence', 0):.0f}%")

                        if stats.get('urgent_signals', 0) > 0:
                            st.error(f"🚨 {stats['urgent_signals']} urgent signals!")
                else:
                    st.info("🤖 AI Bot is analyzing markets...\nWaiting for trading opportunities")

        else:
            st.markdown("""
            <div style="background: rgba(107, 114, 128, 0.2); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid rgba(107, 114, 128, 0.3);">
                <div style="color: #6b7280; font-weight: 600; font-size: 1rem; text-align: center;">🔴 AI Bot Stopped</div>
                <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem; text-align: center; margin-top: 0.3rem;">
                    Start the bot from the Control Center to see live signals
                </div>
            </div>
            """, unsafe_allow_html=True)

    # AI Learning & Optimization Section
    if STRATEGY_ENGINE_AVAILABLE and st.session_state.strategy_engine_initialized:
        st.markdown("### 🧠 AI Learning Insights")

        if hasattr(st.session_state, 'ai_learning_optimizer'):
            # Get learning summary
            learning_summary = st.session_state.ai_learning_optimizer.get_learning_summary()

            if learning_summary.get('total_insights', 0) > 0:
                st.markdown(f"""
                <div style="background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid rgba(139, 92, 246, 0.3);">
                    <div style="color: white; font-weight: 700; font-size: 1rem; text-align: center;">🧠 AI Learning Active</div>
                    <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; text-align: center; margin-top: 0.3rem;">
                        {learning_summary['total_insights']} insights generated
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-top: 0.8rem;">
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Status</div>
                            <div style="color: white; font-size: 0.8rem; font-weight: 600;">{learning_summary['learning_status'].title()}</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Optimizations</div>
                            <div style="color: white; font-size: 0.8rem; font-weight: 600;">{learning_summary['optimization_count']}</div>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)

                # Show recent insights
                recent_insights = learning_summary.get('recent_insights', [])
                if recent_insights:
                    with st.expander("💡 Recent AI Insights", expanded=False):
                        for insight in recent_insights[:3]:
                            insight_type = insight.get('type', 'general')
                            description = insight.get('description', '')
                            recommendation = insight.get('recommendation', '')
                            confidence = insight.get('confidence', 0)

                            # Color code by insight type
                            if insight_type == 'time_optimization':
                                color = "#10b981"
                                icon = "⏰"
                            elif insight_type == 'confidence_calibration':
                                color = "#f59e0b"
                                icon = "🎯"
                            elif insight_type == 'asset_selection':
                                color = "#3b82f6"
                                icon = "📊"
                            else:
                                color = "#8b5cf6"
                                icon = "💡"

                            st.markdown(f"""
                            <div style="background: rgba(139, 92, 246, 0.1); padding: 0.8rem; border-radius: 6px; margin: 0.5rem 0; border-left: 3px solid {color};">
                                <div style="color: {color}; font-weight: 600; font-size: 0.9rem; margin-bottom: 0.3rem;">
                                    {icon} {description}
                                </div>
                                <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; margin-bottom: 0.3rem;">
                                    {recommendation}
                                </div>
                                <div style="color: rgba(255,255,255,0.6); font-size: 0.7rem;">
                                    Confidence: {confidence:.0%}
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                # AI Learning Controls
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("🔍 Analyze Patterns", key="analyze_patterns", help="Analyze recent signal patterns for insights"):
                        with st.spinner("🧠 AI analyzing patterns..."):
                            analysis = st.session_state.ai_learning_optimizer.analyze_signal_patterns(days=7)
                            if analysis.get('status') == 'success':
                                st.success(f"✅ Analysis complete! Found {len(analysis.get('insights', []))} new insights")
                                st.rerun()
                            else:
                                st.warning(f"⚠️ {analysis.get('message', 'Analysis failed')}")

                with col2:
                    if st.button("⚡ Optimize Strategy", key="optimize_strategy", help="Optimize current strategy parameters"):
                        if st.session_state.get('selected_trading_strategy'):
                            with st.spinner("⚡ Optimizing strategy..."):
                                optimization = st.session_state.ai_learning_optimizer.optimize_strategy_parameters(
                                    st.session_state.selected_trading_strategy
                                )
                                if optimization.get('status') != 'error':
                                    st.success("✅ Strategy optimization complete!")
                                    if optimization.get('recommended_changes'):
                                        st.info(f"💡 {len(optimization['recommended_changes'])} improvements suggested")
                                else:
                                    st.warning("⚠️ Optimization failed - insufficient data")
                        else:
                            st.warning("⚠️ No active strategy to optimize")

            else:
                st.info("🧠 AI Learning initializing...\nGenerate signals to enable AI insights")

        else:
            st.warning("⚠️ AI Learning module not available")

    # Signal categories placeholder
    signals_placeholder = st.empty()

# UI placeholders for dynamic content
header_placeholder = st.empty()
alert_placeholder = st.empty()
transparency_placeholder = st.empty()

# --- SCANNING CONFIGURATION (MOVED TO TOP) ---
SCAN_INTERVAL_SECONDS = 10  # Scan interval
PAIRS_PER_SCAN = 3  # Number of pairs to scan per cycle

# --- DEBUG CONFIGURATION ---
# Initialize debug mode in session state
if 'debug_mode' not in st.session_state:
    st.session_state.debug_mode = False  # Set to True for development debugging

def debug_log(message, level="INFO"):
    """Centralized debug logging function"""
    if hasattr(st.session_state, 'debug_mode') and st.session_state.debug_mode:
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")

def api_debug_log(symbol, buy_signals, sell_signals, score):
    """Specific debug logging for API responses"""
    if hasattr(st.session_state, 'debug_mode') and st.session_state.debug_mode:
        debug_log(f"API Response - {symbol}: BUY:{buy_signals}, SELL:{sell_signals}, SCORE:{score}", "API")

def categorization_debug_log(symbol, category, score):
    """Specific debug logging for signal categorization"""
    if hasattr(st.session_state, 'debug_mode') and st.session_state.debug_mode:
        debug_log(f"Categorization - {symbol}: {category} (score: {score})", "CATEGORY")

# Binary Options Configuration
BINARY_OPTIONS_CONFIG = {
    "signal_mapping": {
        "Strong Buy": {"action": "CALL", "confidence": "HIGH"},
        "Weak Buy": {"action": "CALL", "confidence": "MEDIUM"},
        "Strong Sell": {"action": "PUT", "confidence": "HIGH"},
        "Weak Sell": {"action": "PUT", "confidence": "MEDIUM"},
        "Neutral": {"action": "WAIT", "confidence": "LOW"}
    },
    "expiry_times": {
        "5m": 5,
        "15m": 15
    },
    "signal_sensitivity": {
        "high": 15,
        "medium": 20,
        "low": 25
    },
    "pocketoption_pairs": [
        "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD",
        "EURJPY", "EURGBP", "EURAUD", "EURCAD", "EURCHF", "GBPJPY", "AUDJPY",
        "CADJPY", "CHFJPY", "GBPAUD", "GBPCAD", "GBPCHF", "AUDCAD", "AUDCHF",
        "AUDNZD", "CADCHF"
    ]
}

def calculate_signal_expiry(score):
    """Calculate signal expiry time based on score"""
    from datetime import datetime, timedelta

    # Determine expiry minutes based on signal strength
    if abs(score) >= 25:
        expiry_minutes = 5  # Strong signals get shorter expiry
    elif abs(score) >= 20:
        expiry_minutes = 5
    elif abs(score) >= 15:
        expiry_minutes = 15
    else:
        expiry_minutes = 15  # Weaker signals get longer expiry

    expiry_time = datetime.now() + timedelta(minutes=expiry_minutes)
    return expiry_time, expiry_minutes

def should_track_signal(symbol, signal_type, score):
    """Check if a signal should be tracked to prevent duplicates"""
    signal_key = f"{symbol}_{signal_type}_{abs(score):.0f}"
    current_time = datetime.now()

    # Check if we've already tracked this signal recently (within 5 minutes)
    if signal_key in st.session_state.signal_deduplication_cache:
        last_tracked = st.session_state.signal_deduplication_cache[signal_key]
        time_diff = (current_time - last_tracked).total_seconds()
        if time_diff < 300:  # 5 minutes
            return False

    # Update the cache
    st.session_state.signal_deduplication_cache[signal_key] = current_time
    return True

def get_signal_generation_time(symbol, signal_type):
    """Get or create a consistent generation time for a signal"""
    signal_key = f"{symbol}_{signal_type}"
    current_time = datetime.now()

    # If we don't have a generation time for this signal, create one
    if signal_key not in st.session_state.last_signal_generation_time:
        st.session_state.last_signal_generation_time[signal_key] = current_time

    return st.session_state.last_signal_generation_time[signal_key]

def cleanup_old_signal_times():
    """Clean up old signal generation times to prevent memory buildup"""
    current_time = datetime.now()
    keys_to_remove = []

    for signal_key, generation_time in st.session_state.last_signal_generation_time.items():
        # Remove signals older than 1 hour
        if (current_time - generation_time).total_seconds() > 3600:
            keys_to_remove.append(signal_key)

    for key in keys_to_remove:
        del st.session_state.last_signal_generation_time[key]

    # Also cleanup deduplication cache
    keys_to_remove = []
    for signal_key, last_tracked in st.session_state.signal_deduplication_cache.items():
        # Remove entries older than 30 minutes
        if (current_time - last_tracked).total_seconds() > 1800:
            keys_to_remove.append(signal_key)

    for key in keys_to_remove:
        del st.session_state.signal_deduplication_cache[key]

# --- AUTO-REFRESH SETUP ---
# Initialize auto-refresh placeholder
auto_refresh_placeholder = st.empty()

# --- ENHANCED REAL-TIME SCANNING FUNCTION ---
# Run progressive scan cycle with individual pair updates
current_time = datetime.now()

# Initialize progressive update queue if empty
if not st.session_state.progressive_update_queue:
    st.session_state.progressive_update_queue = watchlist.copy()
    import random
    random.shuffle(st.session_state.progressive_update_queue)  # Randomize for better distribution

# Progressive scanning - update pairs individually for better real-time performance
start_idx = st.session_state.scan_index
end_idx = start_idx + PAIRS_PER_SCAN
pairs_to_scan = watchlist[start_idx:end_idx]

# Handle wraparound at end of watchlist
if end_idx > len(watchlist):
    remaining = end_idx - len(watchlist)
    pairs_to_scan.extend(watchlist[:remaining])

# Update scan index for next cycle
st.session_state.scan_index = end_idx % len(watchlist)

# Also process progressive updates for immediate feedback
progressive_pairs = []
if st.session_state.progressive_update_queue:
    # Take up to 2 pairs from progressive queue for immediate updates
    for _ in range(min(2, len(st.session_state.progressive_update_queue))):
        if st.session_state.progressive_update_queue:
            pair = st.session_state.progressive_update_queue.pop(0)
            progressive_pairs.append(pair)
            # Re-add to end of queue for continuous rotation
            st.session_state.progressive_update_queue.append(pair)

# Combine regular scan with progressive updates
all_pairs_to_scan = list(set(pairs_to_scan + progressive_pairs))  # Remove duplicates

# Analyze current batch of pairs with enhanced status tracking
for symbol in all_pairs_to_scan:
    # Update status to "updating"
    update_pair_status(symbol, "updating")

    try:
        # Get unified TradingView data (returns tuple or score)
        result = get_unified_tradingview_data(symbol, timeframe)

        # Handle different return types from the function
        if isinstance(result, tuple):
            # Normal case: (summary_data, chart_data)
            summary_data, chart_data = result
            score = summary_data.get('score', 0)
        elif isinstance(result, (int, float)):
            # Fallback case: just the score
            score = result
        else:
            # Error case: default to 0
            score = 0

        # Log actual scores for troubleshooting (debug mode only)
        debug_log(f"Score Processing - {symbol}: {score} (type: {type(score)})", "SCORE")

        # Remove existing entry for this symbol
        for category in st.session_state.all_results:
            st.session_state.all_results[category] = [
                item for item in st.session_state.all_results.get(category, [])
                if item[0] != symbol
            ]

        # Categorize signal based on score with debug logging
        # Using more sensitive thresholds for better signal detection
        if score >= 10:  # Lowered from 20 to 10
            category = "Strong Buy" if score >= 20 else "Weak Buy"  # Lowered from 40 to 20
            categorization_debug_log(symbol, category, score)
        elif score <= -10:  # Lowered from -20 to -10
            category = "Strong Sell" if score <= -20 else "Weak Sell"  # Lowered from -40 to -20
            categorization_debug_log(symbol, category, score)
        else:
            category = "Neutral"
            categorization_debug_log(symbol, category, score)

        # Add to appropriate category
        if category not in st.session_state.all_results:
            st.session_state.all_results[category] = []
        st.session_state.all_results[category].append((symbol, score))

        # Feed data to automated trading bot if running
        if (STRATEGY_ENGINE_AVAILABLE and
            hasattr(st.session_state, 'automated_trading_bot') and
            st.session_state.automated_trading_bot.is_bot_running()):

            # Check if this symbol should be analyzed by the bot
            if symbol in st.session_state.automated_trading_bot.active_assets:
                # Get market data for bot analysis
                market_data = {
                    'close': get_current_price(symbol) or 1.0000,
                    'score': score,
                    'category': category,
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol
                }

                # Add to bot's analysis cache
                st.session_state.bot_analysis_results[symbol] = market_data

                # Check for new signals from bot
                try:
                    new_signals = []
                    while not st.session_state.automated_trading_bot.signal_queue.empty():
                        signal = st.session_state.automated_trading_bot.signal_queue.get_nowait()
                        new_signals.append(signal)

                        # Add to live signal manager
                        if hasattr(st.session_state, 'live_signal_manager'):
                            st.session_state.live_signal_manager.add_signal(signal)

                    # Update bot performance metrics
                    if new_signals:
                        st.session_state.bot_performance_metrics = st.session_state.automated_trading_bot.get_performance_metrics()

                except Exception as e:
                    debug_log(f"Bot signal processing error: {e}", "ERROR")

        # Update status to "success"
        update_pair_status(symbol, "success")
        st.session_state.api_health_status = "healthy"

    except Exception as e:
        debug_log(f"Update Error - {symbol}: {e}", "ERROR")
        # Update status to "error"
        update_pair_status(symbol, "error")

        # Keep existing data if available, otherwise set to neutral
        found_existing = False
        for cat in st.session_state.all_results:
            for s, sc in st.session_state.all_results[cat]:
                if s == symbol:
                    found_existing = True
                    break
            if found_existing:
                break

        if not found_existing:
            # Add to neutral with score 0 if no existing data
            if "Neutral" not in st.session_state.all_results:
                st.session_state.all_results["Neutral"] = []
            st.session_state.all_results["Neutral"].append((symbol, 0))

        # Update API health status
        if "rate limit" in str(e).lower() or "429" in str(e):
            st.session_state.api_health_status = "rate_limited"
        else:
            st.session_state.api_health_status = "degraded"

# Calculate live dashboard metrics after processing all pairs
calculate_live_dashboard_metrics()

# Cleanup old signal times periodically (every 10th scan)
if st.session_state.scan_index % 10 == 0:
    cleanup_old_signal_times()

# Enhanced signal detection for Signal Hunter mode
current_strong_signals = set()
new_strong_signals = set()

# Process Strong Buy signals
for symbol, score in st.session_state.all_results.get("Strong Buy", []):
    signal_key = f"STRONG BUY: {symbol}"
    current_strong_signals.add(signal_key)

    # Track traditional signal for AI evaluation (only if new signal and should be tracked)
    if (st.session_state.traditional_tracking_enabled and
        hasattr(st.session_state, 'traditional_signal_tracker') and
        st.session_state.traditional_signal_tracker and
        signal_key not in st.session_state.last_strong_signals and
        should_track_signal(symbol, 'Strong Buy', score)):

        # Get current price for the signal
        current_price = get_current_price(symbol)
        if current_price:
            # Get consistent generation time for this signal
            generation_time = get_signal_generation_time(symbol, 'Strong Buy')
            signal_data = {
                'asset': symbol,
                'signal_type': 'Strong Buy',
                'score': score,
                'entry_price': current_price,
                'timeframe': '5m',
                'generation_time': generation_time.strftime('%H:%M:%S'),
                'entry_time': generation_time.strftime('%H:%M:%S'),
                'date': generation_time.strftime('%Y-%m-%d'),
                'indicators': st.session_state.last_api_data.get(symbol, {}),
                'market_conditions': {
                    'timestamp': generation_time.isoformat(),
                    'volatility': 'normal',
                    'trend': 'bullish',
                    'signal_strength': 'strong'
                }
            }
            st.session_state.traditional_signal_tracker.track_traditional_signal(signal_data)
            print(f"📊 Traditional signal tracked: {symbol} Strong Buy @ {generation_time.strftime('%H:%M:%S')}")

    # Check if this is a new signal (Signal Hunter mode)
    if st.session_state.signal_hunter_mode:
        last_timestamp = st.session_state.signal_timestamps.get(f"{symbol}_strong_buy", 0)
        current_timestamp = time.time()

        # Consider signal "new" if it's been more than 30 minutes since last alert
        if current_timestamp - last_timestamp > 1800:  # 30 minutes
            new_strong_signals.add(signal_key)
            st.session_state.signal_timestamps[f"{symbol}_strong_buy"] = current_timestamp
    else:
        # Regular mode: all strong signals are considered "new"
        if signal_key not in st.session_state.last_strong_signals:
            new_strong_signals.add(signal_key)

# Process Strong Sell signals
for symbol, score in st.session_state.all_results.get("Strong Sell", []):
    signal_key = f"STRONG SELL: {symbol}"
    current_strong_signals.add(signal_key)

    # Track traditional signal for AI evaluation (only if new signal and should be tracked)
    if (st.session_state.traditional_tracking_enabled and
        hasattr(st.session_state, 'traditional_signal_tracker') and
        st.session_state.traditional_signal_tracker and
        signal_key not in st.session_state.last_strong_signals and
        should_track_signal(symbol, 'Strong Sell', score)):

        # Get current price for the signal
        current_price = get_current_price(symbol)
        if current_price:
            # Get consistent generation time for this signal
            generation_time = get_signal_generation_time(symbol, 'Strong Sell')
            signal_data = {
                'asset': symbol,
                'signal_type': 'Strong Sell',
                'score': score,
                'entry_price': current_price,
                'timeframe': '5m',
                'generation_time': generation_time.strftime('%H:%M:%S'),
                'entry_time': generation_time.strftime('%H:%M:%S'),
                'date': generation_time.strftime('%Y-%m-%d'),
                'indicators': st.session_state.last_api_data.get(symbol, {}),
                'market_conditions': {
                    'timestamp': generation_time.isoformat(),
                    'volatility': 'normal',
                    'trend': 'bearish',
                    'signal_strength': 'strong'
                }
            }
            st.session_state.traditional_signal_tracker.track_traditional_signal(signal_data)
            print(f"📊 Traditional signal tracked: {symbol} Strong Sell @ {generation_time.strftime('%H:%M:%S')}")

    # Check if this is a new signal (Signal Hunter mode)
    if st.session_state.signal_hunter_mode:
        last_timestamp = st.session_state.signal_timestamps.get(f"{symbol}_strong_sell", 0)
        current_timestamp = time.time()

        # Consider signal "new" if it's been more than 30 minutes since last alert
        if current_timestamp - last_timestamp > 1800:  # 30 minutes
            new_strong_signals.add(signal_key)
            st.session_state.signal_timestamps[f"{symbol}_strong_sell"] = current_timestamp
    else:
        # Regular mode: all strong signals are considered "new"
        if signal_key not in st.session_state.last_strong_signals:
            new_strong_signals.add(signal_key)



    # Simple header with basic status
    with header_placeholder.container():
        progress = f"{min(st.session_state.scan_index, len(watchlist))}/{len(watchlist)}"
        mode_indicator = "🎯 HUNTER" if st.session_state.signal_hunter_mode else "🔄 NORMAL"

        # Simple status display
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(100, 116, 139, 0.3);">
            <div style="color: white; font-weight: 700; font-size: 1.1rem; text-align: center; margin-bottom: 0.5rem;">
                {mode_indicator} Market Scanner
            </div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.9rem; text-align: center;">
                Progress: {progress} • {current_time.strftime('%H:%M:%S')} • {len(current_strong_signals)} Strong Signals
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Enhanced circuit breaker status display
        if st.session_state.circuit_breaker_active:
            remaining_time = st.session_state.circuit_breaker_until - current_time
            minutes_left = int(remaining_time.total_seconds() / 60)
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); padding: 1rem; border-radius: 12px; margin: 0.5rem 0; border: 1px solid rgba(239, 68, 68, 0.3);">
                <div style="display: flex; align-items: center; color: white;">
                    <div style="font-size: 1.5rem; margin-right: 0.8rem;">🛡️</div>
                    <div>
                        <div style="font-weight: 700; font-size: 1rem;">API Circuit Breaker Active</div>
                        <div style="color: rgba(255,255,255,0.9); font-size: 0.9rem;">Protection mode • Cooldown: {minutes_left} minutes remaining</div>
                        <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">Using cached data to maintain signal display</div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)
        elif st.session_state.consecutive_failures > 0:
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%); padding: 1rem; border-radius: 12px; margin: 0.5rem 0; border: 1px solid rgba(245, 158, 11, 0.3);">
                <div style="display: flex; align-items: center; color: white;">
                    <div style="font-size: 1.5rem; margin-right: 0.8rem;">⚠️</div>
                    <div>
                        <div style="font-weight: 700; font-size: 1rem;">API Issues Detected</div>
                        <div style="color: rgba(255,255,255,0.9); font-size: 0.9rem;">Consecutive failures: {st.session_state.consecutive_failures}/3</div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # Simple status display instead of complex dashboard
        with transparency_placeholder.container():
            # Calculate basic metrics
            live_metrics = calculate_live_dashboard_metrics()

            # Simple status display
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
                <div style="color: white; font-weight: 700; font-size: 1rem; text-align: center; margin-bottom: 0.5rem;">📊 Market Status</div>
                <div style="display: flex; justify-content: space-around; text-align: center;">
                    <div>
                        <div style="color: #10b981; font-size: 1.2rem; font-weight: 700;">{live_metrics['bullish_pairs']}</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">Bullish</div>
                    </div>
                    <div>
                        <div style="color: #ef4444; font-size: 1.2rem; font-weight: 700;">{live_metrics['bearish_pairs']}</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">Bearish</div>
                    </div>
                    <div>
                        <div style="color: #3b82f6; font-size: 1.2rem; font-weight: 700;">{live_metrics['strong_signals']}</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">Strong</div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

    # Enhanced display alerts for new strong signals
    if new_strong_signals:
        with alert_placeholder.container():
            alert_title = "🎯 NEW SIGNAL DETECTED!" if st.session_state.signal_hunter_mode else "🚨 STRONG SIGNAL ALERT!"

            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f59e0b 100%); padding: 2rem; border-radius: 20px; margin: 1.5rem 0; text-align: center; position: relative; overflow: hidden; box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3); animation: pulse-alert 2s infinite;">
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%); pointer-events: none;"></div>
                <div style="position: relative; z-index: 1;">
                    <h2 style="color: white; margin: 0 0 1rem 0; font-family: 'Inter', sans-serif; font-size: 2rem; font-weight: 800; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">{alert_title}</h2>
                    <p style="color: rgba(255,255,255,0.9); margin: 0; font-size: 1.1rem; font-weight: 500;">High-probability trading opportunities detected</p>
                </div>
            </div>
            <style>
                @keyframes pulse-alert {{
                    0% {{ box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3); }}
                    50% {{ box-shadow: 0 25px 50px rgba(220, 38, 38, 0.5); }}
                    100% {{ box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3); }}
                }}
            </style>
            """, unsafe_allow_html=True)

            for signal in new_strong_signals:
                # Extract symbol and signal type
                signal_parts = signal.split(": ")
                signal_type = signal_parts[0]
                symbol = signal_parts[1]

                # Get the score for this symbol
                score = 0
                if "BUY" in signal_type:
                    for sym, sc in st.session_state.all_results.get("Strong Buy", []):
                        if sym == symbol:
                            score = sc
                            break
                else:
                    for sym, sc in st.session_state.all_results.get("Strong Sell", []):
                        if sym == symbol:
                            score = sc
                            break

                # Calculate expiry for this signal
                expiry_time, expiry_minutes = calculate_signal_expiry(score)

                # Enhanced alert display
                if "BUY" in signal:
                    signal_color = "#10b981"
                    signal_icon = "🚀"
                    signal_bg = "linear-gradient(135deg, #059669 0%, #10b981 100%)"
                else:
                    signal_color = "#ef4444"
                    signal_icon = "📉"
                    signal_bg = "linear-gradient(135deg, #dc2626 0%, #ef4444 100%)"

                st.markdown(f"""
                <div style="background: {signal_bg}; padding: 1.5rem; border-radius: 16px; margin: 1rem 0; border: 1px solid {signal_color}; box-shadow: 0 8px 25px rgba(0,0,0,0.2);">
                    <div style="display: flex; align-items: center; justify-content: space-between; color: white;">
                        <div style="display: flex; align-items: center;">
                            <div style="font-size: 2rem; margin-right: 1rem;">{signal_icon}</div>
                            <div>
                                <div style="font-size: 1.3rem; font-weight: 700; margin-bottom: 0.3rem;">{signal_type}: {symbol}</div>
                                <div style="font-size: 1rem; color: rgba(255,255,255,0.9);">Score: {score:.0f} • Valid until {expiry_time.strftime('%H:%M')} ({expiry_minutes}m)</div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 8px; font-weight: 600;">
                                {expiry_minutes}min
                            </div>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)

            # Enhanced detection time display
            st.markdown(f"""
            <div style="text-align: center; color: rgba(255,255,255,0.7); font-size: 0.9rem; margin: 1rem 0;">
                ⏰ Detected at {current_time.strftime('%H:%M:%S')} • {len(new_strong_signals)} new signal(s)
            </div>
            """, unsafe_allow_html=True)

            play_alert_sound()
    else:
        alert_placeholder.empty()

    # Update strong signals tracking
    st.session_state.last_strong_signals = current_strong_signals

    # Increment scan iteration for unique button keys
    st.session_state.scan_iteration += 1

    # Display categorized results in the right panel with filtering
    with signals_placeholder.container():
        # Apply signal filtering based on settings
        if st.session_state.show_strong_signals_only:
            # Only show Strong Buy and Strong Sell signals
            categories = ["Strong Buy", "Strong Sell"]
            colors = ["🟢", "🔴"]
            css_classes = ["strong-buy", "strong-sell"]
        else:
            # Show all signal categories
            categories = ["Strong Buy", "Weak Buy", "Neutral", "Weak Sell", "Strong Sell"]
            colors = ["🟢", "🔵", "⚪", "🟠", "🔴"]
            css_classes = ["strong-buy", "weak-buy", "neutral", "weak-sell", "strong-sell"]

        # Filter pairs based on sensitivity if in binary options mode
        filtered_results = {}
        for category in categories:
            pairs = st.session_state.all_results.get(category, [])
            if st.session_state.binary_options_mode:
                # Apply sensitivity filtering
                min_score = BINARY_OPTIONS_CONFIG["signal_sensitivity"][st.session_state.signal_sensitivity]
                filtered_pairs = [(symbol, score) for symbol, score in pairs if abs(score) >= min_score]
                filtered_results[category] = filtered_pairs
            else:
                filtered_results[category] = pairs

        total_pairs = sum(len(pairs) for pairs in filtered_results.values())

        # Check if we should show only strategy signals
        # Now respects the user's choice to show traditional signals alongside AI strategy signals
        show_only_strategy_signals = (
            st.session_state.strategy_mode_enabled and
            st.session_state.strategy_engine_initialized and
            (st.session_state.custom_strategy_text or st.session_state.selected_template) and
            not st.session_state.get('show_traditional_signals', True)  # Hide traditional signals only if user explicitly chooses to
        )

        # Display mode indicator with strategy status
        mode_text = "📞 PocketOption Binary Mode" if st.session_state.binary_options_mode else "📈 Forex Mode"
        filter_text = "Strong Signals Only" if st.session_state.show_strong_signals_only else "All Signals"

        # Enhanced strategy status text
        if st.session_state.strategy_mode_enabled:
            if show_only_strategy_signals:
                strategy_text = "🤖 AI Strategy Only"
            else:
                strategy_text = "🤖 AI Strategy + 📊 Traditional"
        else:
            strategy_text = "📊 Traditional Mode"

        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid rgba(100, 116, 139, 0.3);">
            <div style="color: white; font-weight: 700; font-size: 1rem; text-align: center; margin-bottom: 0.5rem;">{mode_text} • {strategy_text}</div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; text-align: center;">Filter: {filter_text} • Total: {total_pairs} signals</div>
        </div>
        """, unsafe_allow_html=True)

        # Strategy-based signals section (if enabled)
        if st.session_state.strategy_mode_enabled and st.session_state.strategy_engine_initialized:
            # Get strategy to use
            strategy_to_use = None
            strategy_name = "Unknown Strategy"

            if st.session_state.custom_strategy_text:
                strategy_to_use = st.session_state.custom_strategy_text
                strategy_name = "Custom AI Strategy"
            elif st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
                template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
                if template:
                    strategy_to_use = template['strategy_text']
                    strategy_name = template['name']

            if strategy_to_use:
                st.markdown(f"### 🤖 {strategy_name} Signals")

                # Generate strategy-based signals for current symbols
                strategy_signals = []

                # Generate signals for top symbols from all categories
                for category, pairs in st.session_state.all_results.items():
                    for symbol, score in pairs[:3]:  # Limit to top 3 per category
                        try:
                            # Get market data for this symbol, with fallback for circuit breaker mode
                            market_data = st.session_state.last_api_data.get(symbol, {})

                            # If no market data available (circuit breaker mode), create fallback data
                            if not market_data:
                                market_data = {
                                    'score': score,
                                    'buy_signals': max(0, score + 10),
                                    'sell_signals': max(0, -score + 10),
                                    'timestamp': datetime.now(),
                                    'timeframe': '5m'
                                }

                            # Add score to market data
                            market_data['score'] = score
                            market_data['rsi'] = market_data.get('rsi', 50)  # Default values
                            market_data['macd'] = market_data.get('macd', 0)
                            market_data['macd_signal'] = market_data.get('macd_signal', 0)

                            # Determine strategy name - prioritize template over custom text
                            strategy_name = "Unknown Strategy"
                            if st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
                                template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
                                if template:
                                    strategy_name = template['name']
                            elif st.session_state.custom_strategy_text:
                                strategy_name = "Custom AI Strategy"

                            # Generate strategy signal
                            signal = st.session_state.signal_generator.generate_signal(
                                symbol=symbol,
                                market_data=market_data,
                                custom_strategy=strategy_to_use,
                                strategy_name=strategy_name
                            )

                            if signal:

                                # Add signal to tracking system
                                if st.session_state.signal_tracker and st.session_state.performance_tracking_enabled:
                                    st.session_state.signal_tracker.track_signal(signal)

                                strategy_signals.append(signal)
                        except Exception as e:
                            print(f"Error generating strategy signal for {symbol}: {e}")

                # Display strategy signals
                if strategy_signals:
                    for signal in strategy_signals[:10]:  # Show top 10 strategy signals
                        confidence_num = float(signal['confidence'].rstrip('%'))
                        confidence_color = "#10b981" if confidence_num >= 80 else "#f59e0b" if confidence_num >= 60 else "#ef4444"

                        # Format signal timestamp
                        signal_time_str = "Unknown"
                        try:
                            if 'timestamp' in signal:
                                signal_time = datetime.fromisoformat(signal['timestamp'])
                                signal_time_str = signal_time.strftime("%H:%M:%S")
                        except:
                            signal_time_str = datetime.now().strftime("%H:%M:%S")

                        with st.expander(f"🤖 {signal['asset']} • {signal['signal']} • {signal['confidence']} • {signal_time_str}", expanded=False):
                            st.markdown(f"""
                            <div style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%); padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid {confidence_color};">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                    <div style="color: {confidence_color}; font-weight: 700; font-size: 1rem;">🤖 AI Strategy Signal</div>
                                    <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">Generated: {signal_time_str}</div>
                                </div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin: 0.5rem 0;">
                                    <div style="background: {confidence_color}20; padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid {confidence_color}40;">
                                        <div style="color: {confidence_color}; font-size: 1rem; font-weight: 700;">{signal['signal']}</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Action</div>
                                    </div>
                                    <div style="background: rgba(245, 158, 11, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid rgba(245, 158, 11, 0.2);">
                                        <div style="color: #f59e0b; font-size: 1rem; font-weight: 700;">{signal['expiry']}</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Expiry</div>
                                    </div>
                                    <div style="background: rgba(139, 92, 246, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid rgba(139, 92, 246, 0.2);">
                                        <div style="color: #8b5cf6; font-size: 1rem; font-weight: 700;">{signal['confidence']}</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Confidence</div>
                                    </div>
                                    <div style="background: rgba(59, 130, 246, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid rgba(59, 130, 246, 0.2);">
                                        <div style="color: #3b82f6; font-size: 1rem; font-weight: 700;">{signal['entry_price']}</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Entry Price</div>
                                    </div>
                                </div>
                                <div style="background: rgba(0,0,0,0.3); padding: 0.8rem; border-radius: 6px; margin-top: 0.5rem;">
                                    <div style="color: rgba(255,255,255,0.9); font-size: 0.85rem; font-weight: 600;">🧠 Strategy Reasoning:</div>
                                    {chr(10).join(f'<div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; margin-top: 0.2rem;">• {reason}</div>' for reason in signal['reasoning'][:3])}
                                </div>
                                <div style="background: rgba(0,0,0,0.3); padding: 0.8rem; border-radius: 6px; margin-top: 0.5rem;">
                                    <div style="color: rgba(255,255,255,0.9); font-size: 0.85rem; font-weight: 600;">📋 Copy for PocketOption:</div>
                                    <div style="background: rgba(0,0,0,0.5); padding: 0.5rem; border-radius: 4px; margin-top: 0.3rem; font-family: monospace;">
                                        <div style="color: #10b981; font-size: 0.8rem;">Asset: {signal['asset']}</div>
                                        <div style="color: #3b82f6; font-size: 0.8rem;">Action: {signal['signal']}</div>
                                        <div style="color: #f59e0b; font-size: 0.8rem;">Expiry: {signal['expiry']}</div>
                                        <div style="color: #8b5cf6; font-size: 0.8rem;">Confidence: {signal['confidence']}</div>
                                    </div>
                                </div>
                            </div>
                            """, unsafe_allow_html=True)
                else:
                    st.info("🤖 No strategy signals generated. Check your strategy conditions.")
            else:
                st.markdown("""
                <div style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%); padding: 1.5rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(245, 158, 11, 0.2); text-align: center;">
                    <div style="color: #f59e0b; font-size: 1.2rem; font-weight: 700; margin-bottom: 0.5rem;">⚠️ No Strategy Selected</div>
                    <div style="color: rgba(255,255,255,0.9); font-size: 1rem; margin-bottom: 0.5rem;">
                        Please define a custom strategy or select a template to generate AI signals
                    </div>
                    <div style="color: rgba(255,255,255,0.7); font-size: 0.9rem;">
                        Use the AI Strategy Builder in the left panel to get started
                    </div>
                </div>
                """, unsafe_allow_html=True)

        # Performance Dashboard Section
        if st.session_state.strategy_engine_initialized and hasattr(st.session_state, 'performance_evaluator'):
            st.markdown("### 📊 Performance Dashboard")

            try:
                # Get recent performance data
                rolling_24h = st.session_state.performance_database.get_rolling_performance(hours=24)
                rolling_7d = st.session_state.performance_database.get_rolling_performance(hours=168)

                if rolling_24h.get('total_signals', 0) > 0 or rolling_7d.get('total_signals', 0) > 0:
                    # Performance metrics cards
                    st.markdown("""
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin: 0.5rem 0;">
                    """, unsafe_allow_html=True)

                    # 24h performance
                    if rolling_24h.get('total_signals', 0) > 0:
                        win_rate_24h = rolling_24h.get('win_rate', 0)
                        color_24h = "#10b981" if win_rate_24h >= 70 else "#f59e0b" if win_rate_24h >= 60 else "#ef4444"

                        st.markdown(f"""
                        <div style="background: rgba(16, 185, 129, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid {color_24h}40;">
                            <div style="color: {color_24h}; font-size: 1rem; font-weight: 700;">{win_rate_24h:.1f}%</div>
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">24h Win Rate</div>
                            <div style="color: rgba(255,255,255,0.6); font-size: 0.6rem;">{rolling_24h.get('total_signals', 0)} signals</div>
                        </div>
                        """, unsafe_allow_html=True)

                    # 7d performance
                    if rolling_7d.get('total_signals', 0) > 0:
                        win_rate_7d = rolling_7d.get('win_rate', 0)
                        color_7d = "#10b981" if win_rate_7d >= 70 else "#f59e0b" if win_rate_7d >= 60 else "#ef4444"

                        st.markdown(f"""
                        <div style="background: rgba(59, 130, 246, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid {color_7d}40;">
                            <div style="color: {color_7d}; font-size: 1rem; font-weight: 700;">{win_rate_7d:.1f}%</div>
                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">7d Win Rate</div>
                            <div style="color: rgba(255,255,255,0.6); font-size: 0.6rem;">{rolling_7d.get('total_signals', 0)} signals</div>
                        </div>
                        """, unsafe_allow_html=True)

                    st.markdown("</div>", unsafe_allow_html=True)

                    # Top performing strategy
                    try:
                        stats_30d = st.session_state.performance_database.get_performance_stats(days=30)
                        strategies = stats_30d.get('by_strategy', [])
                        if strategies:
                            best_strategy = max(strategies, key=lambda x: x.get('win_rate', 0))
                            if best_strategy.get('evaluated', 0) >= 3:
                                st.markdown(f"""
                                <div style="background: rgba(139, 92, 246, 0.1); padding: 0.5rem; border-radius: 6px; margin: 0.5rem 0; border: 1px solid rgba(139, 92, 246, 0.2);">
                                    <div style="color: #8b5cf6; font-size: 0.8rem; font-weight: 600;">🏆 Top Strategy (30d)</div>
                                    <div style="color: rgba(255,255,255,0.9); font-size: 0.7rem;">{best_strategy['strategy_name'][:20]}...</div>
                                    <div style="color: #10b981; font-size: 0.7rem;">{best_strategy.get('win_rate', 0):.1f}% win rate</div>
                                </div>
                                """, unsafe_allow_html=True)
                    except Exception:
                        pass

                    # Quick action buttons
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("📊 Detailed Report", key=f"detailed_report_{int(time.time())}", help="View comprehensive performance analysis"):
                            # Show detailed performance report
                            try:
                                report = st.session_state.performance_evaluator.generate_performance_report(days=7)
                                if report:
                                    with st.expander("📋 Performance Report (7 days)", expanded=True):
                                        overall = report.get('overall_performance', {}).get('overall', {})
                                        st.markdown(f"**Overall Performance:**")
                                        st.markdown(f"- Total Signals: {overall.get('total_signals', 0)}")
                                        st.markdown(f"- Win Rate: {overall.get('win_rate', 0):.1f}%")
                                        st.markdown(f"- Average Confidence: {overall.get('avg_confidence', 0):.1f}%")

                                        insights = report.get('insights_and_recommendations', [])
                                        if insights:
                                            st.markdown("**Key Insights:**")
                                            for insight in insights[:3]:
                                                st.markdown(f"- {insight}")
                                else:
                                    st.info("📊 No performance data available for detailed report")
                            except Exception as e:
                                st.error(f"Error generating report: {e}")

                    with col2:
                        if st.button("🔄 Refresh Stats", key=f"refresh_stats_{hash('performance_dashboard')}", help="Update performance statistics"):
                            if st.session_state.signal_tracker:
                                st.session_state.signal_tracker.force_evaluation_check()
                                st.success("Stats updated!")
                                st.rerun()

                else:
                    st.info("📊 No performance data yet. Generate AI strategy signals to see analytics!")

                # Pending Signals Display with Real-time Countdown
                # Show strategy-specific title when strategy mode is active
                if show_only_strategy_signals:
                    strategy_name = "Unknown Strategy"
                    if st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
                        template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
                        if template:
                            strategy_name = template['name']
                    elif st.session_state.custom_strategy_text:
                        strategy_name = "Custom AI Strategy"
                    st.markdown(f"### ⏳ Pending Signals - {strategy_name}")
                else:
                    st.markdown("### ⏳ Pending Signals")

                # Initialize countdown timer system
                if 'countdown_timers_initialized' not in st.session_state:
                    st.session_state.countdown_timers_initialized = True

                try:
                    if hasattr(st.session_state, 'performance_database') and st.session_state.performance_database:
                        # Get signals that haven't been evaluated yet
                        import sqlite3
                        with sqlite3.connect(st.session_state.performance_database.db_path) as conn:
                            cursor = conn.cursor()

                            # Filter by strategy if strategy mode is active
                            if show_only_strategy_signals:
                                # Get current strategy name for filtering - prioritize template over custom text
                                current_strategy_name = "Unknown Strategy"
                                if st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
                                    template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
                                    if template:
                                        current_strategy_name = template['name']
                                elif st.session_state.custom_strategy_text:
                                    current_strategy_name = "Custom AI Strategy"

                                cursor.execute("""
                                SELECT s.signal_id, s.asset, s.signal_type, s.entry_price,
                                       s.expiry_time, s.confidence, s.strategy_name, s.timestamp
                                FROM signals s
                                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                                WHERE sr.signal_id IS NULL AND s.strategy_name = ?
                                ORDER BY s.expiry_time ASC
                                LIMIT 5
                                """, (current_strategy_name,))
                            else:
                                cursor.execute("""
                                SELECT s.signal_id, s.asset, s.signal_type, s.entry_price,
                                       s.expiry_time, s.confidence, s.strategy_name, s.timestamp
                                FROM signals s
                                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                                WHERE sr.signal_id IS NULL
                                ORDER BY s.expiry_time ASC
                                LIMIT 5
                                """)

                            pending_signals = cursor.fetchall()

                            if pending_signals:
                                # Generate real-time countdown timer HTML
                                countdown_html = ""
                                timer_data = []

                                for i, signal in enumerate(pending_signals):
                                    signal_id, asset, signal_type, entry_price, expiry_time, confidence, strategy_name, timestamp = signal

                                    try:
                                        from datetime import datetime
                                        expiry_dt = datetime.fromisoformat(expiry_time)
                                        signal_dt = datetime.fromisoformat(timestamp)
                                        now = datetime.now()

                                        time_remaining = expiry_dt - now
                                        total_duration = expiry_dt - signal_dt

                                        # Create unique timer ID
                                        timer_id = f"timer_{signal_id}_{i}"
                                        progress_id = f"progress_{signal_id}_{i}"
                                        card_id = f"card_{signal_id}_{i}"
                                        icon_id = f"icon_{signal_id}_{i}"
                                        urgency_id = f"urgency_{signal_id}_{i}"

                                        # Store timer data for JavaScript
                                        timer_data.append({
                                            'timer_id': timer_id,
                                            'progress_id': progress_id,
                                            'card_id': card_id,
                                            'icon_id': icon_id,
                                            'urgency_id': urgency_id,
                                            'expiry_time': expiry_time,
                                            'total_duration': total_duration.total_seconds() * 1000,  # Convert to milliseconds
                                            'asset': asset,
                                            'signal_type': signal_type
                                        })

                                        if time_remaining.total_seconds() > 0:
                                            minutes_left = int(time_remaining.total_seconds() / 60)
                                            seconds_left = int(time_remaining.total_seconds() % 60)

                                            # Initial color and urgency (will be updated by JavaScript)
                                            if time_remaining.total_seconds() > 5 * 60:
                                                color = "#10b981"
                                                urgency = "Normal"
                                                icon = "⏳"
                                            elif time_remaining.total_seconds() > 2 * 60:
                                                color = "#f59e0b"
                                                urgency = "Warning"
                                                icon = "⚠️"
                                            else:
                                                color = "#ef4444"
                                                urgency = "Urgent"
                                                icon = "🚨" if time_remaining.total_seconds() < 30 else "🔴"

                                            elapsed_time = total_duration.total_seconds() - time_remaining.total_seconds()
                                            progress_percent = (elapsed_time / total_duration.total_seconds()) * 100 if total_duration.total_seconds() > 0 else 0

                                            # Generate HTML with unique IDs for real-time updates
                                            countdown_html += f"""
                                            <div id="{card_id}" class="countdown-card" style="background: linear-gradient(135deg, {color}20, {color}10); padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 2px solid {color}40; transition: all 0.3s ease;">
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                                    <div style="color: {color}; font-weight: 700; font-size: 1.1rem;">
                                                        <span id="{icon_id}">{icon}</span> {asset} • {signal_type}
                                                    </div>
                                                    <div id="{timer_id}" style="color: {color}; font-size: 0.9rem; font-weight: 600;">
                                                        {minutes_left}m {seconds_left}s
                                                    </div>
                                                </div>

                                                <!-- Real-time Progress Bar -->
                                                <div style="background-color: rgba(255, 255, 255, 0.2); border-radius: 10px; height: 12px; overflow: hidden; margin: 0.5rem 0;">
                                                    <div id="{progress_id}" style="height: 100%; width: {progress_percent:.1f}%; background: linear-gradient(90deg, {color}, {color}80); border-radius: 10px; transition: width 0.1s ease, background 0.3s ease;"></div>
                                                </div>

                                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.5rem; margin-top: 0.5rem;">
                                                    <div style="text-align: center;">
                                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Entry</div>
                                                        <div style="color: #3b82f6; font-size: 0.8rem; font-weight: 600;">{entry_price:.5f}</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Confidence</div>
                                                        <div style="color: #8b5cf6; font-size: 0.8rem; font-weight: 600;">{confidence:.0f}%</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Urgency</div>
                                                        <div id="{urgency_id}" style="color: {color}; font-size: 0.8rem; font-weight: 600;">{urgency}</div>
                                                    </div>
                                                </div>

                                                <div style="color: rgba(255,255,255,0.7); font-size: 0.7rem; text-align: center; margin-top: 0.5rem;">
                                                    Strategy: {strategy_name[:25]}{'...' if len(strategy_name) > 25 else ''}
                                                </div>
                                            </div>
                                            """
                                        else:
                                            # Expired signal
                                            countdown_html += f"""
                                            <div style="background: rgba(107, 114, 128, 0.2); padding: 1rem; border-radius: 8px; border: 2px solid #6b728040; opacity: 0.7; margin: 0.5rem 0;">
                                                <div style="color: #6b7280; font-weight: 700; font-size: 1.1rem;">
                                                    ⏰ {asset} • {signal_type} • EXPIRED
                                                </div>
                                                <div style="color: #ef4444; font-size: 0.9rem; margin-top: 0.5rem;">
                                                    Signal expired and is being evaluated...
                                                </div>
                                            </div>
                                            """

                                    except Exception as e:
                                        countdown_html += f"""
                                        <div style="background: rgba(239, 68, 68, 0.1); padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid #ef444440;">
                                            <div style="color: #ef4444; font-weight: 700;">Error processing signal {signal_id}</div>
                                            <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">{str(e)}</div>
                                        </div>
                                        """

                                # Display the countdown HTML
                                st.markdown(countdown_html, unsafe_allow_html=True)

                                # Add real-time JavaScript countdown system
                                if timer_data:
                                    import json
                                    timer_data_json = json.dumps(timer_data)

                                    countdown_js = f"""
                                    <script>
                                    // Real-time countdown timer system
                                    const timerData = {timer_data_json};
                                    let countdownIntervals = [];

                                    // Clear any existing intervals
                                    countdownIntervals.forEach(interval => clearInterval(interval));
                                    countdownIntervals = [];

                                    function updateCountdown(data) {{
                                        const now = new Date().getTime();
                                        const expiryTime = new Date(data.expiry_time).getTime();
                                        const timeRemaining = expiryTime - now;

                                        const timerElement = document.getElementById(data.timer_id);
                                        const progressElement = document.getElementById(data.progress_id);
                                        const cardElement = document.getElementById(data.card_id);
                                        const iconElement = document.getElementById(data.icon_id);
                                        const urgencyElement = document.getElementById(data.urgency_id);

                                        if (!timerElement || !progressElement || !cardElement) return;

                                        if (timeRemaining > 0) {{
                                            // Calculate time components
                                            const minutes = Math.floor(timeRemaining / (1000 * 60));
                                            const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

                                            // Update countdown text
                                            timerElement.textContent = `${{minutes}}m ${{seconds}}s`;

                                            // Calculate progress
                                            const elapsed = data.total_duration - timeRemaining;
                                            const progressPercent = Math.min(100, Math.max(0, (elapsed / data.total_duration) * 100));

                                            // Determine color and urgency based on time remaining
                                            let color, urgency, icon, borderColor;

                                            if (timeRemaining > 5 * 60 * 1000) {{ // > 5 minutes
                                                color = '#10b981'; // Green
                                                urgency = 'Normal';
                                                icon = '⏳';
                                                borderColor = '#10b98140';
                                            }} else if (timeRemaining > 2 * 60 * 1000) {{ // 2-5 minutes
                                                color = '#f59e0b'; // Yellow
                                                urgency = 'Warning';
                                                icon = '⚠️';
                                                borderColor = '#f59e0b40';
                                            }} else {{ // < 2 minutes
                                                color = '#ef4444'; // Red
                                                urgency = 'Urgent';
                                                icon = timeRemaining < 30 * 1000 ? '🚨' : '🔴';
                                                borderColor = '#ef444440';

                                                // Add pulsing animation for urgent signals
                                                if (timeRemaining < 30 * 1000) {{
                                                    cardElement.style.animation = 'pulse 1s infinite';
                                                }} else {{
                                                    cardElement.style.animation = 'none';
                                                }}
                                            }}

                                            // Update progress bar
                                            progressElement.style.width = `${{progressPercent}}%`;
                                            progressElement.style.background = `linear-gradient(90deg, ${{color}}, ${{color}}80)`;

                                            // Update card styling
                                            cardElement.style.background = `linear-gradient(135deg, ${{color}}20, ${{color}}10)`;
                                            cardElement.style.borderColor = borderColor;

                                            // Update timer text color
                                            timerElement.style.color = color;

                                            // Update icon
                                            if (iconElement) {{
                                                iconElement.textContent = icon;
                                            }}

                                            // Update urgency text
                                            if (urgencyElement) {{
                                                urgencyElement.textContent = urgency;
                                                urgencyElement.style.color = color;
                                            }}

                                            // Show urgent notifications
                                            if (timeRemaining < 30 * 1000 && !cardElement.classList.contains('urgent-notified')) {{
                                                cardElement.classList.add('urgent-notified');
                                                showUrgentNotification(data.asset, data.signal_type, Math.floor(timeRemaining / 1000));
                                            }}

                                        }} else {{
                                            // Signal expired
                                            timerElement.textContent = 'EXPIRED';
                                            timerElement.style.color = '#ef4444';
                                            timerElement.style.fontWeight = 'bold';

                                            progressElement.style.width = '100%';
                                            progressElement.style.background = '#6b7280';

                                            cardElement.style.opacity = '0.6';
                                            cardElement.style.animation = 'none';

                                            if (iconElement) {{
                                                iconElement.textContent = '⏰';
                                            }}

                                            if (urgencyElement) {{
                                                urgencyElement.textContent = 'Expired';
                                                urgencyElement.style.color = '#6b7280';
                                            }}
                                        }}
                                    }}

                                    function showUrgentNotification(asset, signalType, secondsLeft) {{
                                        // Create floating notification
                                        const notification = document.createElement('div');
                                        notification.style.cssText = `
                                            position: fixed;
                                            top: 20px;
                                            right: 20px;
                                            background: linear-gradient(135deg, #ef4444, #dc2626);
                                            color: white;
                                            padding: 1rem;
                                            border-radius: 8px;
                                            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                                            z-index: 10000;
                                            animation: slideInRight 0.3s ease;
                                            max-width: 300px;
                                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                                        `;

                                        notification.innerHTML = `
                                            <div style="font-weight: bold; margin-bottom: 0.5rem;">🚨 Signal Expiring Soon!</div>
                                            <div style="font-size: 0.9rem;">${{asset}} ${{signalType}} expires in ${{secondsLeft}} seconds</div>
                                        `;

                                        document.body.appendChild(notification);

                                        // Remove notification after 5 seconds
                                        setTimeout(() => {{
                                            if (notification.parentNode) {{
                                                notification.remove();
                                            }}
                                        }}, 5000);
                                    }}

                                    // Start countdown timers for all signals
                                    timerData.forEach(data => {{
                                        // Initial update
                                        updateCountdown(data);

                                        // Set interval for real-time updates (every 1000ms)
                                        const interval = setInterval(() => {{
                                            updateCountdown(data);
                                        }}, 1000);

                                        countdownIntervals.push(interval);
                                    }});

                                    // Add CSS animations
                                    if (!document.getElementById('countdown-styles')) {{
                                        const style = document.createElement('style');
                                        style.id = 'countdown-styles';
                                        style.textContent = `
                                            @keyframes pulse {{
                                                0% {{ box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }}
                                                70% {{ box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }}
                                                100% {{ box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }}
                                            }}

                                            @keyframes slideInRight {{
                                                from {{ transform: translateX(100%); opacity: 0; }}
                                                to {{ transform: translateX(0); opacity: 1; }}
                                            }}

                                            .countdown-card {{
                                                transition: all 0.3s ease;
                                            }}

                                            .countdown-card:hover {{
                                                transform: translateY(-2px);
                                                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                                            }}
                                        `;
                                        document.head.appendChild(style);
                                    }}
                                    </script>
                                    """

                                    st.markdown(countdown_js, unsafe_allow_html=True)

                                # Smart refresh system - only refresh when signals expire
                                if 'last_signal_check' not in st.session_state:
                                    st.session_state.last_signal_check = 0

                                # Check for expired signals every 60 seconds (less frequent since JS handles real-time updates)
                                import time
                                current_time_int = int(time.time())
                                if current_time_int - st.session_state.last_signal_check > 60:
                                    st.session_state.last_signal_check = current_time_int
                                    # Force evaluation check for expired signals
                                    if st.session_state.signal_tracker:
                                        st.session_state.signal_tracker.force_evaluation_check()

                                # Add notification area for urgent signals
                                notification_placeholder = st.empty()

                                # Check for urgent signals and show Streamlit notifications
                                urgent_signals = []
                                for signal in pending_signals:
                                    try:
                                        expiry_dt = datetime.fromisoformat(signal[4])
                                        time_remaining = expiry_dt - datetime.now()
                                        if 0 < time_remaining.total_seconds() < 30:  # Less than 30 seconds
                                            urgent_signals.append((signal[1], signal[2], int(time_remaining.total_seconds())))
                                    except:
                                        pass

                                # Display urgent notifications in Streamlit
                                if urgent_signals:
                                    with notification_placeholder.container():
                                        for asset, signal_type, seconds_left in urgent_signals:
                                            st.error(f"🚨 **URGENT**: {asset} {signal_type} signal expires in {seconds_left} seconds!")

                                # Add performance metrics for countdown system
                                st.markdown(f"""
                                <div style="background: rgba(59, 130, 246, 0.1); padding: 0.5rem; border-radius: 6px; margin: 0.5rem 0; text-align: center;">
                                    <div style="color: #3b82f6; font-size: 0.8rem;">
                                        🔄 Real-time countdown active • {len(timer_data)} signals monitored • Updates every 1 second
                                    </div>
                                </div>
                                """, unsafe_allow_html=True)
                            else:
                                st.info("⏳ No pending signals. Generate new signals to see them here.")
                    else:
                        st.info("⏳ Signal tracking not available.")

                except Exception as e:
                    st.error(f"Error loading pending signals: {e}")

                # Recent Win/Loss Signals Display
                # Show strategy-specific title when strategy mode is active
                if show_only_strategy_signals:
                    strategy_name = "Unknown Strategy"
                    if st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
                        template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
                        if template:
                            strategy_name = template['name']
                    elif st.session_state.custom_strategy_text:
                        strategy_name = "Custom AI Strategy"
                    st.markdown(f"### 🎯 Recent Signal Results - {strategy_name}")
                else:
                    st.markdown("### 🎯 Recent Signal Results")

                try:
                    if hasattr(st.session_state, 'signal_tracker') and st.session_state.signal_tracker:
                        # Filter recent evaluations by strategy if strategy mode is active
                        if show_only_strategy_signals:
                            # Get current strategy name for filtering - prioritize template over custom text
                            current_strategy_name = "Unknown Strategy"
                            if st.session_state.selected_template and hasattr(st.session_state, 'strategy_templates'):
                                template = st.session_state.strategy_templates.get_template(st.session_state.selected_template)
                                if template:
                                    current_strategy_name = template['name']
                            elif st.session_state.custom_strategy_text:
                                current_strategy_name = "Custom AI Strategy"

                            # Get strategy-filtered recent evaluations
                            recent_evaluations = st.session_state.signal_tracker.get_recent_evaluations_by_strategy(current_strategy_name, limit=5)
                        else:
                            recent_evaluations = st.session_state.signal_tracker.get_recent_evaluations(limit=5)

                        if recent_evaluations:
                            for eval_data in recent_evaluations:
                                result = eval_data.get('result', 'UNKNOWN')
                                asset = eval_data.get('asset', 'UNKNOWN')
                                signal_type = eval_data.get('signal_type', 'UNKNOWN')
                                entry_price = eval_data.get('entry_price', 0)
                                exit_price = eval_data.get('exit_price', 0)
                                profit_loss = eval_data.get('profit_loss', 0)
                                confidence = eval_data.get('confidence', 0)
                                strategy_name = eval_data.get('strategy_name', 'Unknown')
                                evaluated_at = eval_data.get('evaluated_at', 'Unknown')
                                signal_timestamp = eval_data.get('timestamp', 'Unknown')

                                # Color coding based on result
                                if result == 'WIN':
                                    result_color = "#10b981"
                                    result_icon = "✅"
                                    result_bg = "rgba(16, 185, 129, 0.1)"
                                elif result == 'LOSS':
                                    result_color = "#ef4444"
                                    result_icon = "❌"
                                    result_bg = "rgba(239, 68, 68, 0.1)"
                                else:
                                    result_color = "#6b7280"
                                    result_icon = "➖"
                                    result_bg = "rgba(107, 114, 128, 0.1)"

                                # Format evaluated time
                                try:
                                    from datetime import datetime
                                    eval_time = datetime.fromisoformat(evaluated_at.replace('Z', '+00:00'))
                                    time_str = eval_time.strftime("%H:%M")
                                except:
                                    time_str = "Unknown"

                                # Format signal generation time
                                signal_time_str = "Unknown"
                                try:
                                    if signal_timestamp and signal_timestamp != 'Unknown':
                                        signal_time = datetime.fromisoformat(signal_timestamp)
                                        signal_time_str = signal_time.strftime("%H:%M:%S")
                                except:
                                    signal_time_str = "Unknown"

                                # Display signal result card
                                st.markdown(f"""
                                <div style="background: {result_bg}; padding: 0.8rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid {result_color}40;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                        <div style="color: {result_color}; font-weight: 700; font-size: 1rem;">
                                            {result_icon} {asset} • {signal_type} • {result}
                                        </div>
                                        <div style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">
                                            Eval: {time_str}
                                        </div>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">
                                            Strategy: {strategy_name[:20]}{'...' if len(strategy_name) > 20 else ''}
                                        </div>
                                        <div style="color: rgba(255,255,255,0.6); font-size: 0.7rem;">
                                            Generated: {signal_time_str}
                                        </div>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.5rem; margin: 0.5rem 0;">
                                        <div style="text-align: center;">
                                            <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; font-weight: 600;">Entry</div>
                                            <div style="color: #3b82f6; font-size: 0.8rem;">{entry_price:.5f}</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; font-weight: 600;">Exit</div>
                                            <div style="color: #8b5cf6; font-size: 0.8rem;">{exit_price:.5f}</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; font-weight: 600;">P&L</div>
                                            <div style="color: {result_color}; font-size: 0.8rem;">{profit_loss:+.2f}%</div>
                                        </div>
                                    </div>
                                    <div style="display: flex; justify-content: center; align-items: center; margin-top: 0.5rem;">
                                        <div style="color: rgba(255,255,255,0.7); font-size: 0.7rem;">
                                            Confidence: {confidence:.0f}%
                                        </div>
                                    </div>
                                </div>
                                """, unsafe_allow_html=True)
                        else:
                            st.info("🎯 No evaluated signals yet. Signals will appear here after expiry.")
                    else:
                        st.info("🎯 Signal tracking not available. Enable performance tracking to see results.")

                except Exception as e:
                    st.error(f"Error loading recent signals: {e}")

            except Exception as e:
                st.error(f"Error loading performance dashboard: {e}")

        # Traditional signals section - only show if not in strategy-only mode
        if not show_only_strategy_signals:
            # Traditional signals section header
            if st.session_state.strategy_mode_enabled:
                st.markdown("### 📊 Traditional Technical Signals")

            for i, (category, color, css_class) in enumerate(zip(categories, colors, css_classes)):
                pairs = filtered_results.get(category, [])

                # Skip empty categories
                if not pairs:
                    continue

                # Category header with count
                percentage = (len(pairs) / max(1, total_pairs)) * 100 if total_pairs > 0 else 0

                # Convert category name for binary options mode
                if st.session_state.binary_options_mode:
                    if "Strong Buy" in category:
                        display_category = "📞 STRONG CALL"
                        category_color = "#10b981"
                    elif "Weak Buy" in category:
                        display_category = "📈 CALL"
                        category_color = "#3b82f6"
                    elif "Strong Sell" in category:
                        display_category = "📉 STRONG PUT"
                        category_color = "#ef4444"
                    elif "Weak Sell" in category:
                        display_category = "⬇️ PUT"
                        category_color = "#f59e0b"
                    else:
                        display_category = "⏸️ WAIT"
                        category_color = "#6b7280"
                else:
                    display_category = category
                    category_color = "#3b82f6"

                st.markdown(f"""
                <div class="signal-card {css_class}" style="margin: 0.5rem 0;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">{color}</div>
                        <div style="font-weight: 700; color: {category_color}; font-size: 0.9rem;">{display_category}</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">({len(pairs)} pairs)</div>
                        <div class="progress-bar" style="margin: 0.5rem 0;">
                            <div class="progress-fill" style="width: {percentage}%;"></div>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)

                # Individual signals in this category
                for j, (symbol, score) in enumerate(pairs):
                    # Convert to binary signal if in binary options mode
                    if st.session_state.binary_options_mode:
                        binary_signal = convert_to_binary_signal(category, score, symbol)
                        if binary_signal:
                            action = binary_signal["action"]
                            confidence = binary_signal["confidence"]
                            expiry = binary_signal["expiry"]
                            expiry_minutes = binary_signal["expiry_minutes"]
                            signal_color = get_binary_signal_color(action, confidence)
                            signal_icon = get_binary_signal_icon(action, confidence)

                            # Binary options display format
                            display_text = f"{signal_icon} {symbol} • {action} {expiry}"

                            with st.expander(display_text, expanded=False):
                                st.markdown(f"""
                                <div style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%); padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid {signal_color};">
                                    <div style="color: {signal_color}; font-weight: 700; font-size: 1rem; margin-bottom: 0.5rem;">📞 PocketOption Signal</div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin: 0.5rem 0;">
                                        <div style="background: {signal_color}20; padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid {signal_color}40;">
                                            <div style="color: {signal_color}; font-size: 1rem; font-weight: 700;">{action}</div>
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Action</div>
                                        </div>
                                        <div style="background: rgba(245, 158, 11, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid rgba(245, 158, 11, 0.2);">
                                            <div style="color: #f59e0b; font-size: 1rem; font-weight: 700;">{expiry}</div>
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Expiry</div>
                                        </div>
                                        <div style="background: rgba(139, 92, 246, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid rgba(139, 92, 246, 0.2);">
                                            <div style="color: #8b5cf6; font-size: 1rem; font-weight: 700;">{confidence}</div>
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Confidence</div>
                                        </div>
                                        <div style="background: rgba(59, 130, 246, 0.1); padding: 0.5rem; border-radius: 6px; text-align: center; border: 1px solid rgba(59, 130, 246, 0.2);">
                                            <div style="color: #3b82f6; font-size: 1rem; font-weight: 700;">{score:.0f}</div>
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Score</div>
                                        </div>
                                    </div>
                                    <div style="background: rgba(0,0,0,0.3); padding: 0.8rem; border-radius: 6px; margin-top: 0.5rem;">
                                        <div style="color: rgba(255,255,255,0.9); font-size: 0.85rem; font-weight: 600;">📞 PocketOption Instructions:</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; margin-top: 0.3rem;">1. Open {symbol} on PocketOption</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">2. Select {action} option</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">3. Set expiry to {expiry} ({expiry_minutes} minutes)</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">4. Confidence: {confidence}</div>
                                    </div>
                                </div>
                                """, unsafe_allow_html=True)

                                # Chart button for binary options mode
                                # Use hash to create truly unique key
                                import hashlib
                                import time
                                unique_hash = hashlib.md5(f"{symbol}_{category}_{score}_{action}_{expiry}_{time.time()}".encode()).hexdigest()[:8]
                                chart_button_key = f"binary_chart_{unique_hash}"

                                # Show if this symbol is currently selected
                                button_text = f"📊 Open {symbol} Chart"
                                if st.session_state.selected_symbol == symbol:
                                    button_text = f"✅ {symbol} Chart Active"

                                if st.button(
                                    button_text,
                                    key=chart_button_key,
                                    use_container_width=True,
                                    help=f"Open {symbol} chart in center panel",
                                    type="primary" if st.session_state.selected_symbol == symbol else "secondary"
                                ):
                                    # Set the selected symbol and force chart analysis mode
                                    st.session_state.selected_symbol = symbol
                                    st.session_state.chart_analysis_mode = True
                                    st.session_state.chart_session_start = datetime.now()
                                    st.session_state.auto_refresh_paused = True  # Pause auto-refresh for chart analysis
                                    st.success(f"🎯 Opening {symbol} chart in center panel...")
                                    st.rerun()
                        else:
                            continue  # Skip signals not suitable for binary options
                    else:
                        # Traditional forex display
                        expiry_time, expiry_minutes = calculate_signal_expiry(score)

                        # Determine signal color and icon
                        if "Strong Buy" in category:
                            signal_color = "#10b981"
                            signal_icon = "🚀"
                        elif "Weak Buy" in category:
                            signal_color = "#3b82f6"
                            signal_icon = "📈"
                        elif "Strong Sell" in category:
                            signal_color = "#ef4444"
                            signal_icon = "📉"
                        elif "Weak Sell" in category:
                            signal_color = "#f59e0b"
                            signal_icon = "⚠️"
                        else:
                            signal_color = "#6b7280"
                            signal_icon = "➖"

                        # Get consistent signal generation time
                        signal_type_key = "Strong Buy" if "Strong Buy" in category else "Strong Sell" if "Strong Sell" in category else category

                        # Use consistent generation time from our cache
                        if signal_type_key in ["Strong Buy", "Strong Sell"]:
                            generation_time_dt = get_signal_generation_time(symbol, signal_type_key)
                            generation_time = generation_time_dt.strftime("%H:%M:%S")
                            entry_time = generation_time  # Same as generation time
                            signal_tracked = True
                        else:
                            # For non-strong signals, use current time
                            current_time = datetime.now()
                            generation_time = current_time.strftime("%H:%M:%S")
                            entry_time = current_time.strftime("%H:%M:%S")
                            signal_tracked = False

                        # Compact signal card for right panel with timestamps
                        timestamp_indicator = "🕐" if signal_tracked else "⏰"
                        with st.expander(f"{signal_icon} {symbol} • {score:.0f} • {timestamp_indicator}{generation_time}", expanded=False):
                            # Enhanced signal information with timestamps
                            tracking_status = "🔍 AI Tracked" if signal_tracked else "📊 Live Signal"
                            tracking_color = "#10b981" if signal_tracked else "#3b82f6"

                            st.markdown(f"""
                            <div style="background: rgba(16, 185, 129, 0.1); padding: 0.8rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid rgba(16, 185, 129, 0.2);">
                                <div style="color: {tracking_color}; font-weight: 600; font-size: 0.9rem; margin-bottom: 0.5rem;">{tracking_status}</div>
                                <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; margin-bottom: 0.3rem;">
                                    <strong>Generated:</strong> {generation_time}
                                </div>
                                <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem; margin-bottom: 0.3rem;">
                                    <strong>Entry Time:</strong> {entry_time}
                                </div>
                                <div style="color: rgba(255,255,255,0.9); font-size: 0.8rem;">
                                    <strong>Signal Type:</strong> {category}
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                            # Use HTML grid for metrics with timestamps
                            st.markdown(f"""
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.4rem; margin: 0.5rem 0;">
                                <div style="background: rgba(59, 130, 246, 0.1); padding: 0.4rem; border-radius: 6px; text-align: center; border: 1px solid rgba(59, 130, 246, 0.2);">
                                    <div style="color: #3b82f6; font-size: 0.9rem; font-weight: 700;">{score:.1f}</div>
                                    <div style="color: rgba(255,255,255,0.8); font-size: 0.65rem;">Score</div>
                                </div>
                                <div style="background: rgba(245, 158, 11, 0.1); padding: 0.4rem; border-radius: 6px; text-align: center; border: 1px solid rgba(245, 158, 11, 0.2);">
                                    <div style="color: #f59e0b; font-size: 0.9rem; font-weight: 700;">{expiry_minutes}m</div>
                                    <div style="color: rgba(255,255,255,0.8); font-size: 0.65rem;">Expiry</div>
                                </div>
                                <div style="background: rgba(139, 92, 246, 0.1); padding: 0.4rem; border-radius: 6px; text-align: center; border: 1px solid rgba(139, 92, 246, 0.2);">
                                    <div style="color: #8b5cf6; font-size: 0.9rem; font-weight: 700;">{expiry_time.strftime('%H:%M')}</div>
                                    <div style="color: rgba(255,255,255,0.8); font-size: 0.65rem;">Expires At</div>
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                            # Chart button inside expander for better organization
                            # Use hash to create truly unique key
                            import hashlib
                            import time
                            unique_hash = hashlib.md5(f"{symbol}_{category}_{score}_{expiry_minutes}_{time.time()}".encode()).hexdigest()[:8]
                            chart_button_key = f"forex_chart_{unique_hash}"

                            # Show if this symbol is currently selected
                            button_text = f"📊 Open {symbol} Chart"
                            if st.session_state.selected_symbol == symbol:
                                button_text = f"✅ {symbol} Chart Active"

                            if st.button(
                                button_text,
                                key=chart_button_key,
                                use_container_width=True,
                                help=f"Open {symbol} chart in center panel",
                                type="primary" if st.session_state.selected_symbol == symbol else "secondary"
                            ):
                                # Set the selected symbol and force chart analysis mode
                                st.session_state.selected_symbol = symbol
                                st.session_state.chart_analysis_mode = True
                                st.session_state.chart_session_start = datetime.now()
                                st.session_state.auto_refresh_paused = True  # Pause auto-refresh for chart analysis
                                st.success(f"🎯 Opening {symbol} chart in center panel...")
                                st.rerun()
        else:
            # When strategy mode is active and traditional signals are hidden, show informative message
            st.markdown("""
            <div style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%); padding: 1.5rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(16, 185, 129, 0.2); text-align: center;">
                <div style="color: #10b981; font-size: 1.2rem; font-weight: 700; margin-bottom: 0.5rem;">🤖 AI Strategy Only Mode</div>
                <div style="color: rgba(255,255,255,0.9); font-size: 1rem; margin-bottom: 0.5rem;">
                    Showing only signals generated by your selected strategy
                </div>
                <div style="color: rgba(255,255,255,0.7); font-size: 0.9rem; margin-bottom: 0.5rem;">
                    Traditional technical analysis signals are hidden to focus on your strategy results
                </div>
                <div style="color: rgba(255,255,255,0.6); font-size: 0.8rem;">
                    💡 Enable "Show Traditional Signals" in the left panel to see both signal types
                </div>
            </div>
            """, unsafe_allow_html=True)





# --- ENHANCED AUTO-REFRESH MECHANISM WITH CHART ANALYSIS MODE ---
# Intelligent auto-refresh that respects chart analysis sessions
with auto_refresh_placeholder.container():
    # Check if auto-refresh should be paused
    should_pause_refresh = (
        st.session_state.auto_refresh_paused or
        st.session_state.chart_analysis_mode
    )

    if should_pause_refresh:
        # Display paused state with manual controls
        pause_reason = "Chart Analysis Mode" if st.session_state.chart_analysis_mode else "Manual Pause"

        st.markdown(f"""
        <div style="background: linear-gradient(145deg, #d97706 0%, #f59e0b 100%); padding: 1rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(245, 158, 11, 0.3); text-align: center;">
            <div style="color: white; font-size: 1rem; margin-bottom: 0.5rem; font-weight: 600;">
                ⏸️ Auto-Refresh Paused
            </div>
            <div style="color: rgba(255,255,255,0.9); font-size: 0.9rem; margin-bottom: 0.5rem;">
                Reason: {pause_reason}
            </div>
            <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">
                Charts will remain stable for uninterrupted analysis
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Show manual refresh option (centered using HTML instead of columns)
        st.markdown("""
        <div style="display: flex; justify-content: center; margin: 1rem 0;">
        </div>
        """, unsafe_allow_html=True)

        if st.button("🔄 Manual Refresh", key="manual_refresh_bottom", use_container_width=True):
            st.rerun()

        # Don't auto-refresh when paused
        time.sleep(1)  # Small delay to prevent excessive CPU usage

    else:
        # Check if automated trading bot is running for faster updates
        bot_running = (STRATEGY_ENGINE_AVAILABLE and
                      hasattr(st.session_state, 'automated_trading_bot') and
                      st.session_state.automated_trading_bot.is_bot_running())

        if bot_running:
            # Faster refresh for active trading bot
            refresh_interval = 3  # 3 seconds for live trading
            status_text = "🤖 AI Trading Bot Active - Fast Updates"
            status_color = "#10b981"
        else:
            # Normal refresh rate
            refresh_interval = SCAN_INTERVAL_SECONDS
            status_text = f"🔄 Auto-refreshing every {SCAN_INTERVAL_SECONDS} seconds"
            status_color = "#3b82f6"

        st.markdown(f"""
        <div style="background: linear-gradient(145deg, #1e293b 0%, #334155 100%); padding: 1rem; border-radius: 12px; margin: 1rem 0; border: 1px solid rgba(100, 116, 139, 0.3); text-align: center;">
            <div style="color: {status_color}; font-size: 0.9rem; margin-bottom: 0.5rem; font-weight: 600;">
                {status_text}
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 100%; animation: countdown {refresh_interval}s linear infinite; background: {status_color};"></div>
            </div>
            <div style="color: rgba(255,255,255,0.6); font-size: 0.8rem; margin-top: 0.3rem;">
                {'Live signal generation in progress...' if bot_running else 'Next scan cycle starting...'}
            </div>
        </div>
        <style>
            @keyframes countdown {{
                0% {{ width: 100%; }}
                100% {{ width: 0%; }}
            }}
        </style>
        """, unsafe_allow_html=True)

        # Add real-time signal updates notification
        if bot_running and hasattr(st.session_state, 'live_signal_manager'):
            live_signals = st.session_state.live_signal_manager.get_filtered_signals('all')
            if live_signals:
                urgent_count = len([s for s in live_signals if (s.expiry_time - datetime.now()).total_seconds() < 60])
                if urgent_count > 0:
                    st.markdown(f"""
                    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 0.8rem; border-radius: 8px; margin: 0.5rem 0; text-align: center; animation: pulse 2s infinite;">
                        <div style="color: white; font-weight: 700; font-size: 0.9rem;">
                            🚨 {urgent_count} signals expiring within 1 minute!
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

        time.sleep(refresh_interval)
        st.rerun()
