#!/usr/bin/env python3
"""
Test script to verify countdown timer fix for live signals
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from live_signal_manager import LiveSignalManager
from automated_trading_bot import TradingSignal
from datetime import datetime, timedelta
import re

def test_countdown_javascript():
    """Test JavaScript countdown timer generation"""
    print("🔍 Testing Countdown Timer JavaScript Generation...")
    
    # Create test signals with different expiry times
    signals = []
    for i, minutes in enumerate([1, 3, 10], 1):
        signal = TradingSignal(
            signal_id=f'TEST_{i:03d}',
            asset=f'EURUSD',
            action='CALL' if i % 2 else 'PUT',
            entry_price=1.0850 + (i * 0.0001),
            expiry_time=datetime.now() + timedelta(minutes=minutes),
            expiry_minutes=minutes,
            confidence=80.0 + i,
            strategy_name=f'Test_Strategy_{i}',
            reasoning=[f'Test signal {i}'],
            timestamp=datetime.now(),
            risk_level='Medium',
            market_conditions={}
        )
        signals.append(signal)
    
    # Test HTML generation with timers
    lsm = LiveSignalManager()
    for signal in signals:
        lsm.add_signal(signal)
    
    html = lsm.generate_live_signals_html('all')
    
    print(f"✅ JavaScript Timer Test Results:")
    print(f"📊 HTML Length: {len(html)} characters")
    
    # Check for JavaScript components
    js_checks = {
        'Timer Data': 'window.liveTimerData' in html,
        'Update Function': 'updateLiveCountdown' in html,
        'SetInterval': 'setInterval' in html,
        'Error Handling': 'try {' in html,
        'Timer Elements': 'timer-' in html,
        'Progress Elements': 'progress-' in html,
        'Urgency Styling': 'timeRemaining < 30000' in html,
        'CSS Animation': '@keyframes pulse' in html,
        'Global Variables': 'window.liveCountdownIntervals' in html,
        'Backup Timer': 'setInterval(() => {' in html
    }
    
    print("\n🔧 JavaScript Components Check:")
    all_passed = True
    for component, present in js_checks.items():
        status = "✅" if present else "❌"
        print(f"  {status} {component}: {'Present' if present else 'Missing'}")
        if not present:
            all_passed = False
    
    # Check timer IDs are unique
    timer_ids = re.findall(r'timer-[A-Z0-9_]+', html)
    unique_timer_ids = set(timer_ids)
    print(f"\n⏰ Timer IDs: {len(timer_ids)} total, {len(unique_timer_ids)} unique")
    
    if len(timer_ids) == len(unique_timer_ids):
        print("✅ All timer IDs are unique")
    else:
        print("❌ Duplicate timer IDs found")
        all_passed = False
    
    # Check for data attributes
    data_expiry_count = html.count('data-expiry=')
    data_duration_count = html.count('data-total-duration=')
    print(f"\n📊 Data Attributes: {data_expiry_count} expiry, {data_duration_count} duration")
    
    if data_expiry_count > 0 and data_duration_count > 0:
        print("✅ Timer data attributes present")
    else:
        print("❌ Timer data attributes missing")
        all_passed = False
    
    return all_passed

def test_timer_urgency_levels():
    """Test different urgency levels for countdown timers"""
    print("\n🔍 Testing Timer Urgency Levels...")
    
    # Create signals with different urgency levels
    urgency_tests = [
        (0.5, "URGENT", "< 30 seconds"),      # 30 seconds
        (1.5, "WARNING", "< 2 minutes"),     # 1.5 minutes  
        (5, "NORMAL", "> 2 minutes")         # 5 minutes
    ]
    
    lsm = LiveSignalManager()
    
    for i, (minutes, level, description) in enumerate(urgency_tests, 1):
        signal = TradingSignal(
            signal_id=f'URGENCY_{i:03d}',
            asset='GBPUSD',
            action='CALL',
            entry_price=1.2650,
            expiry_time=datetime.now() + timedelta(minutes=minutes),
            expiry_minutes=int(minutes),
            confidence=85.0,
            strategy_name=f'Urgency_Test_{level}',
            reasoning=[f'Urgency test {level}'],
            timestamp=datetime.now(),
            risk_level='Medium',
            market_conditions={}
        )
        lsm.add_signal(signal)
    
    html = lsm.generate_live_signals_html('all')
    
    # Check urgency styling
    urgency_checks = {
        'Urgent Red Styling': '#ef4444' in html,
        'Warning Orange Styling': '#f59e0b' in html,
        'Normal Green Styling': '#10b981' in html,
        'Pulse Animation': 'pulse 1s infinite' in html,
        'Urgency Icons': '🚨' in html and '⚠️' in html and '⏳' in html
    }
    
    print("🎨 Urgency Styling Check:")
    urgency_passed = True
    for check, present in urgency_checks.items():
        status = "✅" if present else "❌"
        print(f"  {status} {check}: {'Present' if present else 'Missing'}")
        if not present:
            urgency_passed = False
    
    return urgency_passed

def test_expired_signals():
    """Test handling of expired signals"""
    print("\n🔍 Testing Expired Signal Handling...")
    
    # Create an expired signal
    expired_signal = TradingSignal(
        signal_id='EXPIRED_001',
        asset='USDJPY',
        action='PUT',
        entry_price=150.25,
        expiry_time=datetime.now() - timedelta(minutes=1),  # Already expired
        expiry_minutes=5,
        confidence=75.0,
        strategy_name='Expired_Test',
        reasoning=['Expired signal test'],
        timestamp=datetime.now() - timedelta(minutes=6),
        risk_level='Low',
        market_conditions={}
    )
    
    lsm = LiveSignalManager()
    lsm.add_signal(expired_signal)
    
    html = lsm.generate_live_signals_html('all')
    
    expired_checks = {
        'Expired Card': 'expired-signal-card' in html,
        'Expired Text': 'EXPIRED' in html,
        'Expired Styling': 'rgba(107, 114, 128, 0.2)' in html,
        'Expired Opacity': 'opacity: 0.7' in html or 'opacity: 0.6' in html
    }
    
    print("⏰ Expired Signal Check:")
    expired_passed = True
    for check, present in expired_checks.items():
        status = "✅" if present else "❌"
        print(f"  {status} {check}: {'Present' if present else 'Missing'}")
        if not present:
            expired_passed = False
    
    return expired_passed

if __name__ == "__main__":
    print("🧪 Countdown Timer Fix Verification Test")
    print("=" * 60)
    
    test1_passed = test_countdown_javascript()
    test2_passed = test_timer_urgency_levels()
    test3_passed = test_expired_signals()
    
    print("\n" + "=" * 60)
    print("📋 FINAL TEST RESULTS:")
    print(f"  🔧 JavaScript Timer System: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  🎨 Urgency Level Styling: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"  ⏰ Expired Signal Handling: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 ALL COUNTDOWN TIMER TESTS PASSED!")
        print("✅ Countdown timer fix is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ SOME COUNTDOWN TIMER TESTS FAILED!")
        print("⚠️ Timer fix needs additional work")
        sys.exit(1)
