#!/usr/bin/env python3
"""
Test script for expired signals WIN/LOSS tracking system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from live_signal_manager import LiveSignalManager
from automated_trading_bot import TradingSignal
from datetime import datetime, timedelta
import random

def test_expired_signals_system():
    """Test complete expired signals tracking system"""
    print("🔍 Testing Expired Signals WIN/LOSS System...")
    
    lsm = LiveSignalManager()
    
    # Create test signals with different results
    test_signals = []
    results = ['WIN', 'LOSS', 'PENDING']
    assets = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD']
    
    for i in range(10):
        # Create expired signal (past expiry time)
        signal = TradingSignal(
            signal_id=f'EXPIRED_{i:03d}',
            asset=random.choice(assets),
            action=random.choice(['CALL', 'PUT']),
            entry_price=1.0850 + random.uniform(-0.01, 0.01),
            expiry_time=datetime.now() - timedelta(minutes=random.randint(1, 60)),
            expiry_minutes=random.choice([1, 5, 15]),
            confidence=random.uniform(70, 95),
            strategy_name=f'Test_Strategy_{i}',
            reasoning=[f'Test signal {i}'],
            timestamp=datetime.now() - timedelta(minutes=random.randint(5, 120)),
            risk_level=random.choice(['Low', 'Medium', 'High']),
            market_conditions={}
        )
        
        # Add to live signals first
        lsm.add_signal(signal)
        
        # Then expire with result
        result = random.choice(results)
        exit_price = signal.entry_price + random.uniform(-0.002, 0.002)
        lsm.expire_signal(signal.signal_id, result, signal.entry_price, exit_price)
        
        test_signals.append(signal)
    
    print(f"✅ Created {len(test_signals)} test expired signals")
    print(f"📊 Live signals: {len(lsm.live_signals)}")
    print(f"📊 Expired signals: {len(lsm.expired_signals)}")
    
    return lsm

def test_filtering_system(lsm):
    """Test expired signals filtering"""
    print("\n🔍 Testing Filtering System...")
    
    # Test result filters
    filters = ['all', 'win', 'loss', 'pending']
    for filter_type in filters:
        filtered = lsm.get_filtered_expired_signals(filter_type, 'all')
        print(f"  📋 {filter_type.upper()} filter: {len(filtered)} signals")
    
    # Test date filters
    date_filters = ['all', 'today', 'yesterday', 'this_week']
    for date_filter in date_filters:
        filtered = lsm.get_filtered_expired_signals('all', date_filter)
        print(f"  📅 {date_filter.upper()} filter: {len(filtered)} signals")
    
    return True

def test_html_generation(lsm):
    """Test expired signals HTML generation"""
    print("\n🔍 Testing HTML Generation...")
    
    # Test different filter combinations
    test_combinations = [
        ('all', 'all'),
        ('win', 'today'),
        ('loss', 'today'),
        ('pending', 'all')
    ]
    
    for result_filter, date_filter in test_combinations:
        html = lsm.generate_expired_signals_html(result_filter, date_filter)
        print(f"  🎨 HTML ({result_filter}, {date_filter}): {len(html)} characters")
        
        # Check for key components
        checks = {
            'Statistics Header': 'Total' in html and 'WIN' in html and 'LOSS' in html,
            'Signal Cards': 'EURUSD' in html or 'GBPUSD' in html,
            'Result Icons': '✅' in html or '❌' in html or '⏳' in html,
            'Price Data': 'Entry Price' in html and 'Exit Price' in html,
            'Date/Time': any(str(year) in html for year in [2024, 2025])
        }
        
        for check, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"    {status} {check}")
    
    return True

def test_statistics_calculation(lsm):
    """Test expired signals statistics"""
    print("\n🔍 Testing Statistics Calculation...")
    
    stats = lsm.get_expired_signal_statistics('all')
    
    if stats:
        print(f"  📊 Total Signals: {stats.get('total_signals', 0)}")
        print(f"  📊 WIN Signals: {stats.get('win_signals', 0)}")
        print(f"  📊 LOSS Signals: {stats.get('loss_signals', 0)}")
        print(f"  📊 Win Rate: {stats.get('win_rate', 0):.1f}%")
        print(f"  📊 Best Asset: {stats.get('best_asset', 'N/A')}")
        print(f"  📊 Worst Asset: {stats.get('worst_asset', 'N/A')}")
        
        # Check asset performance
        asset_perf = stats.get('asset_performance', {})
        print(f"  📊 Asset Performance: {len(asset_perf)} assets tracked")
        
        for asset, perf in asset_perf.items():
            print(f"    📈 {asset}: {perf['wins']}/{perf['total']} ({perf['win_rate']:.1f}%)")
        
        return True
    else:
        print("  ❌ No statistics generated")
        return False

def test_profit_loss_calculation(lsm):
    """Test profit/loss calculation"""
    print("\n🔍 Testing Profit/Loss Calculation...")
    
    # Test CALL signal
    call_signal = TradingSignal(
        signal_id='TEST_CALL',
        asset='EURUSD',
        action='CALL',
        entry_price=1.0850,
        expiry_time=datetime.now() - timedelta(minutes=5),
        expiry_minutes=5,
        confidence=85.0,
        strategy_name='Test',
        reasoning=['Test'],
        timestamp=datetime.now(),
        risk_level='Medium',
        market_conditions={}
    )
    
    # Test WIN scenario (exit price > entry price for CALL)
    win_pl = lsm._calculate_profit_loss(call_signal, 1.0860)  # Higher price = WIN for CALL
    print(f"  📈 CALL WIN P&L: {win_pl} (expected: 1.0)")
    
    # Test LOSS scenario (exit price < entry price for CALL)
    loss_pl = lsm._calculate_profit_loss(call_signal, 1.0840)  # Lower price = LOSS for CALL
    print(f"  📉 CALL LOSS P&L: {loss_pl} (expected: -1.0)")
    
    # Test PUT signal
    put_signal = TradingSignal(
        signal_id='TEST_PUT',
        asset='EURUSD',
        action='PUT',
        entry_price=1.0850,
        expiry_time=datetime.now() - timedelta(minutes=5),
        expiry_minutes=5,
        confidence=85.0,
        strategy_name='Test',
        reasoning=['Test'],
        timestamp=datetime.now(),
        risk_level='Medium',
        market_conditions={}
    )
    
    # Test WIN scenario (exit price < entry price for PUT)
    put_win_pl = lsm._calculate_profit_loss(put_signal, 1.0840)  # Lower price = WIN for PUT
    print(f"  📈 PUT WIN P&L: {put_win_pl} (expected: 1.0)")
    
    # Test LOSS scenario (exit price > entry price for PUT)
    put_loss_pl = lsm._calculate_profit_loss(put_signal, 1.0860)  # Higher price = LOSS for PUT
    print(f"  📉 PUT LOSS P&L: {put_loss_pl} (expected: -1.0)")
    
    # Verify calculations
    calculations_correct = (
        win_pl == 1.0 and 
        loss_pl == -1.0 and 
        put_win_pl == 1.0 and 
        put_loss_pl == -1.0
    )
    
    return calculations_correct

if __name__ == "__main__":
    print("🧪 Expired Signals WIN/LOSS Tracking Test")
    print("=" * 60)
    
    # Run all tests
    lsm = test_expired_signals_system()
    test1_passed = test_filtering_system(lsm)
    test2_passed = test_html_generation(lsm)
    test3_passed = test_statistics_calculation(lsm)
    test4_passed = test_profit_loss_calculation(lsm)
    
    print("\n" + "=" * 60)
    print("📋 FINAL TEST RESULTS:")
    print(f"  🔧 Expired Signals System: {'✅ PASSED' if len(lsm.expired_signals) > 0 else '❌ FAILED'}")
    print(f"  📋 Filtering System: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  🎨 HTML Generation: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"  📊 Statistics Calculation: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    print(f"  💰 P&L Calculation: {'✅ PASSED' if test4_passed else '❌ FAILED'}")
    
    all_passed = (
        len(lsm.expired_signals) > 0 and 
        test1_passed and 
        test2_passed and 
        test3_passed and 
        test4_passed
    )
    
    if all_passed:
        print("\n🎉 ALL EXPIRED SIGNALS TESTS PASSED!")
        print("✅ WIN/LOSS tracking system is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ SOME EXPIRED SIGNALS TESTS FAILED!")
        print("⚠️ System needs additional work")
        sys.exit(1)
